#!/usr/bin/env python3
"""
Test script for DHCP integration with HotSpot search
اختبار تكامل DHCP مع البحث في HotSpot
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from simple_bot2 import SimpleMikroTikBot

async def test_dhcp_leases():
    """Test DHCP leases functionality"""
    print("🧪 اختبار وظيفة DHCP leases...")
    
    try:
        # Create bot instance
        bot = SimpleMikroTikBot()
        
        # Test DHCP leases retrieval
        print("📡 جاري الحصول على DHCP leases...")
        dhcp_result = await bot.get_dhcp_leases()
        
        if dhcp_result['success']:
            leases = dhcp_result['leases']
            print(f"✅ تم الحصول على {len(leases)} DHCP lease")
            
            # Show first few leases as example
            count = 0
            for mac, lease_info in leases.items():
                if count >= 3:  # Show only first 3
                    break
                print(f"  📍 MAC: {mac}")
                print(f"     🏠 Hostname: {lease_info.get('hostname', 'غير متوفر')}")
                print(f"     🌐 IP: {lease_info.get('ip', 'غير متوفر')}")
                print(f"     📊 Status: {lease_info.get('status', 'غير متوفر')}")
                print()
                count += 1
                
            if len(leases) > 3:
                print(f"... و {len(leases) - 3} lease أخرى")
        else:
            print(f"❌ فشل في الحصول على DHCP leases: {dhcp_result['error']}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار DHCP leases: {e}")

async def test_hotspot_active_users():
    """Test HotSpot active users with MAC addresses"""
    print("\n🧪 اختبار وظيفة HotSpot active users مع MAC addresses...")
    
    try:
        # Create bot instance
        bot = SimpleMikroTikBot()
        
        # Test HotSpot active users with MAC
        print("📡 جاري الحصول على HotSpot active users...")
        active_result = await bot.get_hotspot_active_users_with_mac()
        
        if active_result['success']:
            users = active_result['users']
            print(f"✅ تم الحصول على {len(users)} مستخدم نشط")
            
            # Show first few users as example
            count = 0
            for username, user_info in users.items():
                if count >= 3:  # Show only first 3
                    break
                print(f"  👤 Username: {username}")
                print(f"     🔗 MAC: {user_info.get('mac', 'غير متوفر')}")
                print(f"     🌐 IP: {user_info.get('ip', 'غير متوفر')}")
                print(f"     ⏱️ Uptime: {user_info.get('uptime', 'غير متوفر')}")
                print()
                count += 1
                
            if len(users) > 3:
                print(f"... و {len(users) - 3} مستخدم آخر")
        else:
            print(f"❌ فشل في الحصول على HotSpot active users: {active_result['error']}")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار HotSpot active users: {e}")

async def test_hostname_lookup():
    """Test hostname lookup from DHCP lease"""
    print("\n🧪 اختبار البحث عن hostname من DHCP lease...")
    
    try:
        # Create bot instance
        bot = SimpleMikroTikBot()
        
        # First get some MAC addresses from active users
        active_result = await bot.get_hotspot_active_users_with_mac()
        
        if active_result['success'] and active_result['users']:
            # Test with first available MAC address
            first_user = next(iter(active_result['users'].values()))
            test_mac = first_user.get('mac')
            
            if test_mac and test_mac != 'غير متوفر':
                print(f"🔍 اختبار البحث عن hostname للـ MAC: {test_mac}")
                
                dhcp_info = await bot.get_hostname_from_dhcp_lease(test_mac)
                
                if dhcp_info:
                    print("✅ تم العثور على معلومات DHCP:")
                    print(f"  🏠 Hostname: {dhcp_info.get('hostname', 'غير متوفر')}")
                    print(f"  🌐 IP: {dhcp_info.get('ip', 'غير متوفر')}")
                    print(f"  📊 Status: {dhcp_info.get('status', 'غير متوفر')}")
                else:
                    print("❌ لم يتم العثور على معلومات DHCP لهذا الـ MAC")
            else:
                print("⚠️ لا يوجد MAC address متاح للاختبار")
        else:
            print("⚠️ لا يوجد مستخدمين نشطين للاختبار")
            
    except Exception as e:
        print(f"❌ خطأ في اختبار hostname lookup: {e}")

async def main():
    """Main test function"""
    print("🚀 بدء اختبار تكامل DHCP مع HotSpot")
    print("=" * 50)
    
    # Test individual components
    await test_dhcp_leases()
    await test_hotspot_active_users()
    await test_hostname_lookup()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")
    print("\n📝 ملاحظات:")
    print("- إذا نجحت جميع الاختبارات، فالميزة جاهزة للاستخدام")
    print("- يمكنك الآن البحث في HotSpot وسترى أسماء الأجهزة من DHCP")
    print("- استخدم /start في البوت واختر 'البحث في الهوت سبوت'")

if __name__ == "__main__":
    asyncio.run(main())
