#!/usr/bin/env python3
"""
Simple MikroTik Bot
بوت مبسط لإدارة MikroTik - ميزات أساسية فقط
"""

import os
import sys
import json
import logging
import asyncio
import time
from datetime import datetime

# Set UTF-8 encoding for console output
if sys.platform == "win32":
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, errors='replace')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, errors='replace')

# Set environment variable for UTF-8
os.environ['PYTHONIOENCODING'] = 'utf-8'

# Import Telegram libraries
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes, MessageHandler, filters
from telegram.constants import ParseMode

# Import RouterOS API
try:
    import routeros_api
except ImportError:
    print("❌ RouterOS API library not found. Install with: pip install routeros-api")
    sys.exit(1)

# Setup logging with UTF-8 encoding and log rotation
from logging.handlers import RotatingFileHandler

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        RotatingFileHandler('simple_bot.log', maxBytes=5*1024*1024, backupCount=3, encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleMikroTikBot:
    """Simple MikroTik Bot with basic features only"""
    
    def __init__(self):
        """Initialize the bot"""
        logger.info("Initializing Simple MikroTik Bot...")

        # Load configuration
        self.load_config()

        # Load authorized users
        self.load_authorized_users()

        # Create Telegram application
        self.application = Application.builder().token(self.bot_token).build()

        # Setup handlers
        self.setup_handlers()

        # Initialize user search mode
        self.user_search_mode = None
        self.search_type = None

        # Initialize current connection
        self.current_connection = None

        logger.info("Simple MikroTik Bot initialized successfully")
    
    def load_config(self):
        """Load configuration from config.json"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Telegram settings
            self.bot_token = config['telegram']['bot_token']
            self.admin_users = set(config['telegram']['authorized_users'])
            
            # MikroTik settings
            self.mikrotik_host = config['mikrotik']['host']
            self.mikrotik_ddns_host = config['mikrotik'].get('ddns_host', '')
            self.mikrotik_connection_type = config['mikrotik'].get('connection_type', 'ip')
            self.mikrotik_port = config['mikrotik']['port']
            self.mikrotik_username = config['mikrotik']['username']
            self.mikrotik_password = config['mikrotik']['password']
            self.mikrotik_use_ssl = config['mikrotik']['use_ssl']
            self.mikrotik_ssl_verify = config['mikrotik']['ssl_verify']
            self.mikrotik_timeout = config['mikrotik']['timeout']
            
            # Determine actual host
            if self.mikrotik_connection_type == 'ddns' and self.mikrotik_ddns_host:
                self.actual_host = self.mikrotik_ddns_host
            else:
                self.actual_host = self.mikrotik_host
            
            # System info
            self.system_name = config['system']['name']
            self.system_version = config['system']['version']
            
            logger.info("Configuration loaded successfully")
            logger.info(f"MikroTik Host: {self.actual_host}:{self.mikrotik_port}")

        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    def load_authorized_users(self):
        """Load authorized users from authorized_users.json"""
        try:
            with open('authorized_users.json', 'r', encoding='utf-8') as f:
                users_data = json.load(f)
            
            self.authorized_users = set()
            for user_id in users_data['users'].keys():
                self.authorized_users.add(int(user_id))
            
            logger.info(f"Loaded {len(self.authorized_users)} authorized users")

        except Exception as e:
            logger.error(f"Failed to load authorized users: {e}")
            # Fallback to admin users only
            self.authorized_users = self.admin_users.copy()
    
    def setup_handlers(self):
        """Setup command and callback handlers"""
        # Command handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("help", self.help_command))
        
        # Callback query handler
        self.application.add_handler(CallbackQueryHandler(self.button_callback))
        
        # Message handler for search input
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_text_input)
        )
        
        # Error handler
        self.application.add_error_handler(self.error_handler)
        
        logger.info("Handlers setup completed")
    
    def is_authorized(self, user_id):
        """Check if user is authorized"""
        return user_id in self.authorized_users or user_id in self.admin_users
    
    def is_admin(self, user_id):
        """Check if user is admin"""
        return user_id in self.admin_users

    def create_connection(self):
        """Create or reuse MikroTik connection"""
        if self.current_connection is not None:
            try:
                # Test if connection is still valid
                api = self.current_connection.get_api()
                api.get_resource('/system/identity').get()
                return self.current_connection
            except Exception:
                logger.info("Existing connection invalid, creating new one")
                self.current_connection = None

        try:
            connection = routeros_api.RouterOsApiPool(
                host=self.actual_host,
                username=self.mikrotik_username,
                password=self.mikrotik_password,
                port=self.mikrotik_port,
                plaintext_login=True,
                use_ssl=self.mikrotik_use_ssl,
                ssl_verify=False,
                ssl_verify_hostname=False
            )
            self.current_connection = connection
            return connection
        except Exception as e:
            logger.error(f"Failed to create connection: {e}")
            raise

    async def discover_network_devices(self):
        """Discover devices in the network using IP Neighbor Discovery only"""
        devices = []

        try:
            logger.debug(f"Connecting to MikroTik at {self.actual_host}:{self.mikrotik_port}")

            connection = self.create_connection()
            api = connection.get_api()
            logger.debug("Successfully connected to MikroTik API")

            try:
                logger.debug("Getting /ip/neighbor resource")
                neighbors = api.get_resource('/ip/neighbor')
                neighbor_list = neighbors.get()

                logger.debug(f"Found {len(neighbor_list)} neighbors")

                for neighbor in neighbor_list:
                    logger.debug(f"Raw neighbor data: {neighbor}")

                    device = {
                        'ip': self._safe_decode(neighbor.get('address', '')),
                        'mac': self._safe_decode(neighbor.get('mac-address', '')),
                        'interface': self._safe_decode(neighbor.get('interface', '')),
                        'status': 'reachable',
                        'source': 'IP Neighbor Discovery',
                        'hostname': self._safe_decode(neighbor.get('identity', '')),
                        'last_seen': self._safe_decode(neighbor.get('age', ''))
                    }
                    devices.append(device)
                    logger.debug(f"Processed device: {device}")

                connection.disconnect()
                logger.debug(f"Successfully discovered {len(devices)} devices")
                return {"success": True, "devices": devices}

            except Exception as e:
                logger.error(f"Error in API operations: {e}")
                connection.disconnect()
                raise e

        except Exception as e:
            logger.error(f"Error discovering network devices: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {"success": False, "error": str(e), "devices": []}

    def test_connection_simple(self):
        """Test connection without complex operations"""
        try:
            logger.info(f"Testing connection to {self.actual_host}:{self.mikrotik_port}")
            logger.info(f"Using credentials: {self.mikrotik_username}/{'*' * len(self.mikrotik_password)}")
            logger.info(f"SSL enabled: {self.mikrotik_use_ssl}")

            connection = self.create_connection()
            api = connection.get_api()

            # Simple test - get system identity
            identity = api.get_resource('/system/identity')
            result = identity.get()

            connection.disconnect()

            if result:
                device_name = result[0].get('name', 'Unknown')
                logger.info(f"Connection successful! Device: {device_name}")
                return True, device_name
            else:
                logger.error("No response from device")
                return False, "No response"

        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False, str(e)
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        user = update.effective_user
        
        if not self.is_authorized(user.id):
            await update.message.reply_text(
                "❌ **غير مصرح لك باستخدام هذا البوت**\n\n"
                "يرجى التواصل مع المدير للحصول على الصلاحية.",
                parse_mode=ParseMode.MARKDOWN
            )
            return
        
        user_role = "admin" if self.is_admin(user.id) else "user"
        
        message_text = f"""🤖 البوت الذكي لإدارة MikroTik

مرحباً {user.first_name}!

الجهاز المتصل:
• العنوان: {self.actual_host}:{self.mikrotik_port} ({'DDNS' if self.mikrotik_connection_type == 'ddns' else 'IP'})
• المستخدم: {self.mikrotik_username}
• دورك: {user_role}

اختر من القائمة:"""

        message = message_text
        
        keyboard = [
            [InlineKeyboardButton("🔧 اختبار الاتصال", callback_data="test_connection")],
            [
                InlineKeyboardButton("📡 البحث عن أجهزة", callback_data="discover_devices"),
                InlineKeyboardButton("🔍 مين فاصل", callback_data="who_disconnected")
            ],
            [
                InlineKeyboardButton("🏨 البحث في الهوت سبوت", callback_data="simple_search_hotspot"),
                InlineKeyboardButton("🔒 لسة متشحنش", callback_data="userman_safe_search")
            ],
            [
                InlineKeyboardButton("🔍 البحث المتطابق", callback_data="exact_match_search")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

    async def send_startup_notification(self):
        """Send startup notification to all authorized users"""
        try:
            # Test MikroTik connection first
            connection_status = "❌ غير متصل"
            try:
                success, message = self.test_connection_simple()
                if success:
                    connection_status = "✅ متصل"
                    logger.info(f"MikroTik connection test successful: {message}")
                else:
                    connection_status = f"❌ خطأ: {message}"
                    logger.warning(f"MikroTik connection test failed: {message}")
            except Exception as e:
                connection_status = f"❌ خطأ: {str(e)}"
                logger.error(f"Error testing MikroTik connection: {e}")

            # Get current timestamp
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Create startup message
            startup_message = f"""🤖 **مرحبا! البوت جاهز للعمل**

✅ **حالة النظام:**
• الاتصال بـ MikroTik: {connection_status}
• الجهاز: {self.actual_host}:{self.mikrotik_port}
• المستخدمون المصرحون: {len(self.authorized_users)}
• وقت البدء: {current_time}

🎯 **البوت جاهز لاستقبال الأوامر!**

اختر من القائمة أدناه:"""

            # Create main menu keyboard
            keyboard = [
                [InlineKeyboardButton("🔧 اختبار الاتصال", callback_data="test_connection")],
                [
                    InlineKeyboardButton("📡 البحث عن أجهزة", callback_data="discover_devices"),
                    InlineKeyboardButton("🔍 مين فاصل", callback_data="who_disconnected")
                ],
                [
                    InlineKeyboardButton("🏨 البحث في الهوت سبوت", callback_data="simple_search_hotspot"),
                    InlineKeyboardButton("🔒 لسة متشحنش", callback_data="userman_safe_search")
                ],
                [
                    InlineKeyboardButton("🔍 البحث المتطابق", callback_data="exact_match_search")
                ]
            ]

            reply_markup = InlineKeyboardMarkup(keyboard)

            # Send notification to all authorized users
            successful_sends = 0
            failed_sends = 0

            # Combine admin users and authorized users to ensure all get the notification
            all_users = self.authorized_users.union(self.admin_users)

            for user_id in all_users:
                try:
                    await self.application.bot.send_message(
                        chat_id=user_id,
                        text=startup_message,
                        parse_mode=ParseMode.MARKDOWN,
                        reply_markup=reply_markup
                    )
                    successful_sends += 1
                    logger.info(f"Startup notification sent successfully to user {user_id}")
                except Exception as e:
                    failed_sends += 1
                    logger.warning(f"Failed to send startup notification to user {user_id}: {e}")

            logger.info(f"Startup notifications sent: {successful_sends} successful, {failed_sends} failed")

        except Exception as e:
            logger.error(f"Error sending startup notifications: {e}")

    async def monitor_bot_health(self):
        """Monitor bot health and log status periodically"""
        try:
            while True:
                await asyncio.sleep(300)  # Check every 5 minutes

                # Log bot status
                logger.info("Bot health check - Bot is running normally")

                # Check if application is still running
                if self.application and self.application.updater:
                    if self.application.updater.running:
                        logger.debug("Updater is running normally")
                    else:
                        logger.warning("Updater is not running!")
                else:
                    logger.error("Application or updater is None!")

        except asyncio.CancelledError:
            logger.info("Bot health monitor cancelled")
        except Exception as e:
            logger.error(f"Error in bot health monitor: {e}")

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        if not self.is_authorized(update.effective_user.id):
            return

        help_text = f"""
📚 **{self.system_name}**

**الأوامر:**
• `/start` - القائمة الرئيسية
• `/help` - المساعدة

**الميزات:**
🔧 اختبار الاتصال
📡 البحث عن أجهزة
🔍 مين فاصل
🏨 البحث في الهوت سبوت
        """
        
        keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="back_to_main")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            help_text,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle button callbacks"""
        query = update.callback_query
        await query.answer()

        callback_data = query.data
        user_id = query.from_user.id
        
        if not self.is_authorized(user_id):
            await query.answer("❌ غير مصرح لك", show_alert=True)
            return
        
        try:
            if callback_data == "test_connection":
                await self.handle_connection_test(query)
            elif callback_data == "discover_devices":
                await self.handle_device_search_input(query)
            elif callback_data == "who_disconnected":
                await self.handle_who_disconnected(query)
            elif callback_data == "simple_search_hotspot":
                await self.handle_simple_hotspot_search(query)
            elif callback_data == "hotspot_search_name":
                await self.handle_hotspot_search_by_name(query)
            elif callback_data == "hotspot_search_comment":
                await self.handle_hotspot_search_by_comment(query)
            elif callback_data == "userman_safe_search":
                await self.handle_userman_safe_search(query)
            elif callback_data == "device_show_all":
                await self.handle_device_show_all(query)
            elif callback_data.startswith("device_search_page_"):
                await self.handle_device_search_pagination(query, callback_data)
            elif callback_data == "device_search_refresh":
                await self.handle_device_search_refresh(query)
            elif callback_data.startswith("hotspot_search_page_"):
                await self.handle_hotspot_search_pagination(query, callback_data)
            elif callback_data.startswith("hotspot_comment_search_page_"):
                await self.handle_hotspot_comment_search_pagination(query, callback_data)
            elif callback_data == "exact_match_search":
                await self.handle_exact_match_search(query)
            elif callback_data == "exact_search_by_name":
                await self.handle_exact_search_by_name(query)
            elif callback_data == "exact_search_by_comment":
                await self.handle_exact_search_by_comment(query)
            elif callback_data == "exact_search_by_exact_comment":
                await self.handle_exact_search_by_exact_comment(query)
            elif callback_data == "direct_reset_server":
                await self.handle_direct_reset_server(query)
            elif callback_data == "direct_reset_by_comment":
                await self.handle_direct_reset_by_comment(query)
            elif callback_data == "direct_reset_by_name":
                await self.handle_direct_reset_by_name(query)
            elif callback_data == "direct_reset_special":
                await self.handle_direct_reset_special(query)
            elif callback_data == "direct_reset_special_by_comment":
                await self.handle_direct_reset_special_by_comment(query)
            elif callback_data == "direct_reset_special_by_name":
                await self.handle_direct_reset_special_by_name(query)
            elif callback_data.startswith("exact_reset_data_"):
                await self.handle_exact_reset_data(query, callback_data)
            elif callback_data.startswith("exact_restore_card_"):
                await self.handle_exact_restore_card(query, callback_data)
            elif callback_data.startswith("confirm_exact_reset_"):
                await self.confirm_exact_reset_data(query, callback_data)
            elif callback_data.startswith("confirm_exact_restore_"):
                await self.confirm_exact_restore_card(query, callback_data)
            elif callback_data == "back_to_main":
                await self.show_main_menu(query)
            else:
                await query.answer("⚠️ هذه الميزة قيد التطوير", show_alert=True)
        
        except Exception as e:
            logger.error(f"Error in button callback: {e}")
            await query.answer("❌ حدث خطأ، يرجى المحاولة مرة أخرى", show_alert=True)
    
    async def show_main_menu(self, query):
        """Show main menu"""
        user = query.from_user
        user_role = "admin" if self.is_admin(user.id) else "user"
        
        message_text = f"""🤖 البوت الذكي لإدارة MikroTik

مرحباً {user.first_name}!

الجهاز المتصل:
• العنوان: {self.actual_host}:{self.mikrotik_port} ({'DDNS' if self.mikrotik_connection_type == 'ddns' else 'IP'})
• المستخدم: {self.mikrotik_username}
• دورك: {user_role}

اختر من القائمة:"""

        message = message_text
        
        keyboard = [
            [InlineKeyboardButton("🔧 اختبار الاتصال", callback_data="test_connection")],
            [
                InlineKeyboardButton("📡 البحث عن أجهزة", callback_data="discover_devices"),
                InlineKeyboardButton("🔍 مين فاصل", callback_data="who_disconnected")
            ],
            [
                InlineKeyboardButton("🏨 البحث في الهوت سبوت", callback_data="simple_search_hotspot"),
                InlineKeyboardButton("🔒 لسة متشحنش", callback_data="userman_safe_search")
            ],
            [
                InlineKeyboardButton("🔍 البحث المتطابق", callback_data="exact_match_search")
            ]
        ]
        
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def handle_connection_test(self, query):
        """Handle connection test"""
        await query.edit_message_text("🔧 **جاري اختبار الاتصال...**")

        try:
            success, result = self.test_connection_simple()

            if success:
                device_name = result if isinstance(result, str) else 'Unknown'

                message = f"""
🔧 **اختبار الاتصال**

✅ **الاتصال ناجح!**

**معلومات الجهاز:**
• الاسم: `{device_name}`
• العنوان: `{self.actual_host}:{self.mikrotik_port}`
• نوع الاتصال: `{'SSL' if self.mikrotik_use_ssl else 'عادي'}`
• المنفذ: `{self.mikrotik_port}`

**الحالة:** 🟢 متصل ويعمل بشكل طبيعي

✅ **جميع الاختبارات نجحت!**
• 🔗 اختبار الاتصال: نجح
• 🔐 اختبار المصادقة: نجح
• ⚡ اختبار تنفيذ الأوامر: نجح
                """
            else:
                error_msg = result
                if "invalid user name or password" in error_msg.lower():
                    error_detail = "اسم المستخدم أو كلمة المرور غير صحيحة"
                elif "timeout" in error_msg.lower():
                    error_detail = "انتهت مهلة الاتصال"
                elif "connection refused" in error_msg.lower():
                    error_detail = "تم رفض الاتصال - تأكد من تفعيل API"
                elif "no route to host" in error_msg.lower():
                    error_detail = "لا يمكن الوصول للجهاز - تحقق من العنوان"
                else:
                    error_detail = error_msg

                message = f"""
🔧 **اختبار الاتصال**

❌ **فشل الاتصال!**

**السبب:** {error_detail}

**يرجى التحقق من:**
• إعدادات الشبكة والاتصال
• بيانات تسجيل الدخول في config.json
• تفعيل API في MikroTik
• حالة جهاز MikroTik
• إعدادات الجدار الناري
                """

        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            error_msg = str(e)
            if "timeout" in error_msg.lower():
                error_detail = "انتهت مهلة الاتصال"
            elif "invalid user name or password" in error_msg.lower():
                error_detail = "اسم المستخدم أو كلمة المرور غير صحيحة"
            elif "authentication" in error_msg.lower() or "login" in error_msg.lower():
                error_detail = "خطأ في المصادقة"
            elif "connection refused" in error_msg.lower():
                error_detail = "تم رفض الاتصال - تأكد من تفعيل API"
            elif "no route to host" in error_msg.lower():
                error_detail = "لا يمكن الوصول للجهاز - تحقق من العنوان"
            else:
                error_detail = error_msg

            message = f"""
🔧 **اختبار الاتصال**

❌ **فشل الاتصال!**

**السبب:** {error_detail}

**يرجى التحقق من:**
• إعدادات الشبكة والاتصال
• بيانات تسجيل الدخول
• تفعيل API في MikroTik
• حالة جهاز MikroTik
            """

        keyboard = [[InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

    async def handle_device_search_input(self, query):
        """Handle device search input request"""
        user = query.from_user

        self.user_search_mode = user.id
        self.search_type = "device_search"

        message = """
🔍 **البحث عن جهاز**

أدخل: IP أو MAC أو اسم الجهاز
        """

        keyboard = [
            [
                InlineKeyboardButton("📊 عرض الكل", callback_data="device_show_all"),
                InlineKeyboardButton("❌ إلغاء", callback_data="back_to_main")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            message,
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )

    async def handle_device_show_all(self, query):
        """Handle show all devices"""
        await query.edit_message_text("📡 **جاري البحث عن الأجهزة في الشبكة...**")

        try:
            result = await self.discover_network_devices()

            if result['success']:
                devices = result['devices']
                response_text = f"📡 **تقرير الأجهزة في الشبكة**\n\n"
                response_text += f"🔍 **إجمالي الأجهزة المكتشفة:** {len(devices)}\n\n"

                active_devices = [d for d in devices if d['status'] in ['reachable', 'bound', 'up']]
                inactive_devices = [d for d in devices if d['status'] in ['incomplete', 'down', 'waiting']]

                if active_devices:
                    response_text += f"✅ **الأجهزة النشطة ({len(active_devices)}):**\n"
                    for i, device in enumerate(active_devices[:5]):
                        hostname = device['hostname'] if device['hostname'] and device['hostname'] != 'غير متوفر' else 'غير معروف'

                        response_text += f"**جهاز {i+1}:**\n"
                        response_text += f"• **الاسم:** `{hostname}`\n"
                        response_text += f"• **IP:** `{device['ip']}`\n"
                        response_text += f"• **MAC:** `{device['mac']}`\n\n"

                    if len(active_devices) > 5:
                        response_text += f"... و {len(active_devices) - 5} جهاز نشط آخر\n\n"

                if inactive_devices:
                    response_text += f"⚠️ **الأجهزة غير النشطة ({len(inactive_devices)}):**\n"
                    for i, device in enumerate(inactive_devices[:3]):
                        hostname = device['hostname'] if device['hostname'] and device['hostname'] != 'غير متوفر' else 'غير معروف'
                        response_text += f"• **الاسم:** `{hostname}` - **IP:** `{device['ip']}` - **MAC:** `{device['mac']}`\n"

                    if len(inactive_devices) > 3:
                        response_text += f"... و {len(inactive_devices) - 3} جهاز غير نشط آخر\n\n"

                if not devices:
                    response_text += "📭 **لم يتم العثور على أجهزة**\n\nلا توجد أجهزة مكتشفة في الشبكة."

                message = response_text
            else:
                message = f"❌ **فشل في البحث عن الأجهزة**\n\n<code>{result['error']}</code>"

        except Exception as e:
            logger.error(f"Device discovery failed: {e}")
            error_msg = str(e)
            if "invalid user name or password" in error_msg.lower():
                error_detail = "اسم المستخدم أو كلمة المرور غير صحيحة"
            elif "timeout" in error_msg.lower():
                error_detail = "انتهت مهلة الاتصال"
            elif "connection refused" in error_msg.lower():
                error_detail = "تم رفض الاتصال"
            else:
                error_detail = "خطأ في الاتصال"

            message = f"""
📡 **البحث عن أجهزة**

❌ **فشل البحث!**

**السبب:** {error_detail}

**الحل:**
• تحقق من بيانات تسجيل الدخول
• تأكد من تفعيل API في MikroTik
• تحقق من الاتصال بالشبكة
            """

        keyboard = [[InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

    async def handle_who_disconnected(self, query):
        """Handle who disconnected check - Show only disconnected devices using Netwatch"""
        await query.edit_message_text("🔍 **جاري فحص الأجهزة المنقطعة باستخدام Netwatch...**")

        try:
            connection = self.create_connection()
            api = connection.get_api()

            netwatch = api.get_resource('/tool/netwatch')
            results = netwatch.get()

            # Do not disconnect here to reuse the connection

            all_hosts = results if results else []
            up_hosts = []
            down_hosts = []
            disabled_hosts = []

            for entry in all_hosts:
                try:
                    status = str(entry.get('status', '')).lower()
                    disabled = str(entry.get('disabled', 'false'))

                    if disabled == 'true':
                        disabled_hosts.append(entry)
                    elif status == 'down':
                        down_hosts.append(entry)
                    elif status == 'up':
                        up_hosts.append(entry)
                except Exception as e:
                    logger.debug(f"Error processing netwatch entry: {e}")
                    continue

            response_text = f"🔍 **مين فاصل**\n\n"
            response_text += f"� المراقبة: `{len(all_hosts)}` | النشطة: `{len(up_hosts)}` | المنقطعة: `{len(down_hosts)}` | المعطلة: `{len(disabled_hosts)}`\n\n"

            if down_hosts:
                response_text += f"❌ **المنقطعة ({len(down_hosts)}):**\n"
                for i, entry in enumerate(down_hosts):
                    try:
                        host = self._safe_decode(entry.get('host', 'غير معروف'))
                        comment = self._safe_decode(entry.get('comment', ''))
                        since = self._safe_decode(entry.get('since', 'غير متوفر'))
                        timeout = str(entry.get('timeout', 'غير محدد'))
                        interval = str(entry.get('interval', 'غير محدد'))

                        response_text += f"**{i+1}.** `{host}` ❌"
                        if comment and comment != 'غير متوفر' and comment != 'خطأ في التشفير':
                            response_text += f" - {comment}"
                        response_text += "\n"
                    except Exception as e:
                        logger.debug(f"Error processing down host entry: {e}")
                        response_text += f"**{i+1}.** خطأ\n"

            if disabled_hosts:
                response_text += f"⚠️ **المعطلة ({len(disabled_hosts)}):**\n"
                for i, entry in enumerate(disabled_hosts[:3]):
                    try:
                        host = self._safe_decode(entry.get('host', 'غير محدد'))
                        comment = self._safe_decode(entry.get('comment', ''))
                        response_text += f"• `{host}`"
                        if comment and comment != 'غير متوفر' and comment != 'خطأ في التشفير':
                            response_text += f" - {comment}"
                        response_text += "\n"
                    except Exception as e:
                        logger.debug(f"Error processing disabled host entry: {e}")
                        response_text += f"• خطأ في قراءة البيانات\n"

                if len(disabled_hosts) > 3:
                    response_text += f"... و {len(disabled_hosts) - 3} آخر\n"
                response_text += "\n"

            if down_hosts:
                response_text += f"🔴 **منقطعة: {len(down_hosts)}**"
            else:
                response_text += f"✅ **جميع الأجهزة تعمل**"

            if not all_hosts:
                response_text = f"⚠️ **لا توجد أجهزة مراقبة**"

            message = response_text

        except Exception as e:
            logger.error(f"Who disconnected check failed: {e}")
            error_msg = str(e)

            if "codec can't decode" in error_msg.lower() or "utf-8" in error_msg.lower():
                error_detail = "مشكلة في تشفير البيانات"
            elif "invalid user name or password" in error_msg.lower():
                error_detail = "اسم المستخدم أو كلمة المرور غير صحيحة"
            elif "timeout" in error_msg.lower():
                error_detail = "انتهت مهلة الاتصال"
            elif "connection refused" in error_msg.lower():
                error_detail = "تم رفض الاتصال"
            else:
                error_detail = "خطأ في الاتصال"

            message = f"""❌ **فشل الفحص**

{error_detail}"""

        keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="back_to_main")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

    async def handle_simple_hotspot_search(self, query):
        """Handle simple hotspot search"""
        message = """
🏨 **البحث في الهوت سبوت**

اختر نوع البحث:
        """

        keyboard = [
            [
                InlineKeyboardButton("👤 بالاسم", callback_data="hotspot_search_name"),
                InlineKeyboardButton("💬 بالتعليق", callback_data="hotspot_search_comment")
            ],
            [InlineKeyboardButton("🔙 العودة", callback_data="back_to_main")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

    async def handle_userman_safe_search(self, query):
        """Handle safe User Manager search"""
        message = """
🔒 **البحث في User Manager**

أدخل اسم المستخدم الكامل
        """

        keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="back_to_main")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

        self.user_search_mode = query.from_user.id
        self.search_type = "usermanager_safe_search"

    async def handle_hotspot_search_by_name(self, query):
        """Handle hotspot search by name"""
        message = """
🏨 **البحث بالاسم**

أرسل اسم المستخدم
        """

        keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="simple_search_hotspot")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

        self.user_search_mode = query.from_user.id
        self.search_type = "hotspot_name"

    async def handle_hotspot_search_by_comment(self, query):
        """Handle hotspot search by comment"""
        message = """
🏨 **البحث بالتعليق**

أرسل التعليق
        """

        keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="simple_search_hotspot")]]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

        self.user_search_mode = query.from_user.id
        self.search_type = "hotspot_comment"

    async def handle_text_input(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text input for search"""
        user_id = update.effective_user.id

        if not self.is_authorized(user_id):
            return

        if self.user_search_mode != user_id:
            return

        search_query = update.message.text.strip()

        if not search_query:
            await update.message.reply_text("❌ يرجى إدخال نص للبحث")
            return

        search_type = getattr(self, 'search_type', 'hotspot')

        self.user_search_mode = None
        self.search_type = None

        if search_type == "userman":
            await self.search_userman_user(update, search_query)
        elif search_type == "device_search":
            await self.perform_device_search(update, search_query)
        elif search_type == "usermanager_safe_search":
            await self.process_usermanager_safe_search(update, search_query)
        elif search_type == "hotspot_name":
            await self.search_hotspot_user_by_name(update, search_query)
        elif search_type == "hotspot_comment":
            await self.search_hotspot_user_by_comment(update, search_query)
        elif search_type == "exact_search_name":
            logger.info(f"Processing exact search by name for: {search_query}")
            await self.process_exact_search_by_name(update, search_query)
        elif search_type == "exact_search_comment":
            logger.info(f"Processing exact search by comment for: {search_query}")
            await self.process_exact_search_by_comment(update, search_query)
        elif search_type == "exact_search_exact_comment":
            logger.info(f"Processing exact search by exact comment for: {search_query}")
            await self.process_exact_search_by_exact_comment(update, search_query)
        elif search_type == "direct_reset_by_comment":
            logger.info(f"Processing direct reset by comment for: {search_query}")
            await self.process_direct_reset_by_comment(update, search_query)
        elif search_type == "direct_reset_by_name":
            logger.info(f"Processing direct reset by name for: {search_query}")
            await self.process_direct_reset_by_name(update, search_query)
        elif search_type == "direct_reset_special_by_comment":
            logger.info(f"Processing direct reset special by comment for: {search_query}")
            await self.process_direct_reset_special_by_comment(update, search_query)
        elif search_type == "direct_reset_special_by_name":
            logger.info(f"Processing direct reset special by name for: {search_query}")
            await self.process_direct_reset_special_by_name(update, search_query)
        else:
            await self.search_hotspot_user_by_name(update, search_query)

    async def search_hotspot_user_by_name(self, update, search_query, page=0, per_page=5):
        """Search for hotspot user with pagination and detailed data usage info"""
        # Initialize variables to avoid undefined variable errors
        total_users = 0
        end_idx = 0
        found_users = []

        try:
            connection = self.create_connection()
            api = connection.get_api()

            hotspot_users = api.get_resource('/ip/hotspot/user')
            users_list = hotspot_users.get()

            # Get active users
            hotspot_active = api.get_resource('/ip/hotspot/active')
            active_users_list = hotspot_active.get()

            connection.disconnect()

            # Create set of active usernames for quick lookup and store IP addresses and MAC addresses
            active_usernames = set()
            active_users_with_info = {}  # Dictionary to store username -> {IP, MAC} mapping
            for active_user in active_users_list:
                active_username = self._safe_decode(active_user.get('user', ''))
                active_ip = self._safe_decode(active_user.get('address', ''))
                active_mac = self._safe_decode(active_user.get('mac-address', ''))
                if active_username:
                    active_usernames.add(active_username.lower())
                    # Store the IP address and MAC address for this active user
                    active_users_with_info[active_username.lower()] = {
                        'ip': active_ip,
                        'mac': active_mac if active_mac else 'غير متوفر'
                    }

            # Enhanced filter - search in username, comment, and email
            found_users = []
            for user in users_list:
                username = self._safe_decode(user.get('name', ''))
                comment = self._safe_decode(user.get('comment', ''))
                email = self._safe_decode(user.get('email', ''))

                # Search in multiple fields
                if (search_query.lower() in username.lower() or
                    search_query.lower() in comment.lower() or
                    search_query.lower() in email.lower()):
                    # Add active status, IP address, and MAC address to user data
                    user['is_active'] = username.lower() in active_usernames
                    active_info = active_users_with_info.get(username.lower(), {})
                    user['active_ip'] = active_info.get('ip', '')
                    user['active_mac'] = active_info.get('mac', 'غير متوفر')

                    # Always initialize DHCP hostname field
                    user['dhcp_hostname'] = 'غير متوفر'

                    # Try to get hostname from DHCP lease for active users
                    if user['is_active'] and user['active_mac'] != 'غير متوفر':
                        logger.info(f"Looking up DHCP hostname for active user {username} with MAC: {user['active_mac']}")
                        dhcp_info = await self.get_hostname_from_dhcp_lease(user['active_mac'])
                        if dhcp_info:
                            hostname = dhcp_info.get('hostname', '')
                            if hostname and hostname.strip():
                                user['dhcp_hostname'] = hostname
                                logger.info(f"Found DHCP hostname: {user['dhcp_hostname']} for user: {username}")
                            else:
                                logger.info(f"Empty DHCP hostname found for user: {username}")
                        else:
                            logger.info(f"No DHCP hostname found for user: {username}")
                    else:
                        logger.info(f"User {username} is not active or has no MAC address")

                    found_users.append(user)

            total_users = len(found_users)
            start_idx = page * per_page
            end_idx = start_idx + per_page
            page_users = found_users[start_idx:end_idx]

            if total_users > 0:
                escaped_query = self._escape_markdown(search_query)
                message = f"""🔍 **نتائج البحث عن:** `{escaped_query}`

📊 **إجمالي النتائج:** {total_users} مستخدم
📄 **الصفحة:** {page + 1} من {(total_users - 1) // per_page + 1}

🏨 **مستخدمي الهوت سبوت:**

"""

                # Show results for current page with detailed info
                for i, user in enumerate(page_users, start=start_idx + 1):
                    username = self._escape_markdown(self._safe_decode(user.get('name', 'غير معروف')))
                    profile = self._escape_markdown(self._safe_decode(user.get('profile', 'غير محدد')))
                    disabled = user.get('disabled', 'false')
                    status_icon = "❌" if disabled == 'true' else "✅"
                    active_icon = "🟢" if user.get('is_active', False) else "⚪"

                    # Get data usage information
                    bytes_in = int(user.get('bytes-in', 0))  # Actually Upload (عكس)
                    bytes_out = int(user.get('bytes-out', 0))  # Actually Download (عكس)
                    limit_bytes = int(user.get('limit-bytes-total', 0))  # Total limit
                    total_used = bytes_in + bytes_out

                    # Format using helper function (عكس القيم)
                    upload_text = self._format_bytes(bytes_in)  # bytes-in = رفع
                    download_text = self._format_bytes(bytes_out)  # bytes-out = تحميل
                    total_used_text = self._format_bytes(total_used)

                    # Calculate remaining data
                    if limit_bytes > 0:
                        remaining_bytes = limit_bytes - total_used
                        limit_text = self._format_bytes(limit_bytes)
                        remaining_text = self._format_bytes(remaining_bytes) if remaining_bytes > 0 else "منتهية"
                    else:
                        remaining_text = "غير محدود"
                        limit_text = "غير محدود"

                    message += f"**{i}.** `{username}` {status_icon} {active_icon}\n"
                    message += f"    📋 **الملف:** {profile}\n"

                    # Always show DHCP hostname field for active users
                    if user.get('is_active', False):
                        hostname = user.get('dhcp_hostname', 'غير متوفر')
                        message += f"    🏠 **اسم الجهاز النشط:** {hostname}\n"

                    # Show IP address if user is active
                    if user.get('is_active', False) and user.get('active_ip'):
                        active_ip = str(user.get('active_ip'))  # IP addresses in backticks don't need escaping
                        message += f"    🌐 **عنوان IP:** `{active_ip}`\n"

                    # Data usage details (عكس التحميل والرفع)
                    message += f"    📊 **استخدام البيانات:**\n"
                    message += f"        📥 **تحميل:** {download_text}\n"
                    message += f"        📤 **رفع:** {upload_text}\n"
                    message += f"        📈 **إجمالي مستخدم:** {total_used_text}\n"
                    message += f"        🎯 **الحد الأقصى:** {limit_text}\n"
                    message += f"        💾 **متبقي:** {remaining_text}\n"

                    # Add email if available
                    email = self._escape_markdown(self._safe_decode(user.get('email', '')))
                    if email and email != 'غير متوفر' and email.strip():
                        message += f"    📧 **البريد:** {email}\n"

                    # Add comment if available
                    comment = self._escape_markdown(self._safe_decode(user.get('comment', '')))
                    if comment and comment != 'غير متوفر' and comment.strip():
                        message += f"    💬 **التعليق:** {comment}\n"

                    message += "\n"

                message += """📝 **الرموز:**
• ✅ مفعل | ❌ معطل | 🟢 متصل | ⚪ غير متصل
• 🌐 عنوان IP يظهر فقط للمستخدمين المتصلين حالياً
• 📊 البيانات بالوحدة المناسبة (B, KB, MB, GB)"""
            else:
                escaped_query = self._escape_markdown(search_query)
                message = f"""🔍 **نتائج البحث عن:** `{escaped_query}`

📭 **لا توجد نتائج**

لم يتم العثور على أي مستخدمين يطابقون البحث في نظام الهوت سبوت."""

        except Exception as e:
            logger.error(f"Hotspot user search failed: {e}")
            error_msg = str(e)
            if "invalid user name or password" in error_msg.lower():
                error_detail = "اسم المستخدم أو كلمة المرور غير صحيحة"
            elif "timeout" in error_msg.lower():
                error_detail = "انتهت مهلة الاتصال"
            elif "connection refused" in error_msg.lower():
                error_detail = "تم رفض الاتصال"
            else:
                error_detail = "خطأ في الاتصال"

            message = f"""❌ **فشل البحث**

{error_detail}"""

        keyboard = []

        if total_users > per_page:
            nav_buttons = []
            if page > 0:
                nav_buttons.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"hotspot_search_page_{page-1}_{search_query}"))

            if end_idx < total_users:
                nav_buttons.append(InlineKeyboardButton("➡️ التالي", callback_data=f"hotspot_search_page_{page+1}_{search_query}"))

            if nav_buttons:
                keyboard.append(nav_buttons)

        action_buttons = [
            InlineKeyboardButton("🔍 بحث جديد", callback_data="hotspot_search_name"),
            InlineKeyboardButton("🔙 العودة", callback_data="back_to_main")
        ]
        keyboard.append(action_buttons)

        reply_markup = InlineKeyboardMarkup(keyboard)

        self.hotspot_search_results = {
            'users': found_users,
            'search_term': search_query,
            'per_page': per_page
        }

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

    async def search_hotspot_user_by_comment(self, update, search_query, page=0, per_page=5):
        """Search for hotspot user by comment with pagination and detailed data usage info"""
        # Initialize variables to avoid undefined variable errors
        total_users = 0
        end_idx = 0
        found_users = []

        try:
            connection = self.create_connection()
            api = connection.get_api()

            hotspot_users = api.get_resource('/ip/hotspot/user')
            users_list = hotspot_users.get()

            # Get active users
            hotspot_active = api.get_resource('/ip/hotspot/active')
            active_users_list = hotspot_active.get()

            connection.disconnect()

            # Create set of active usernames for quick lookup and store IP addresses and MAC addresses
            active_usernames = set()
            active_users_with_info = {}  # Dictionary to store username -> {IP, MAC} mapping
            for active_user in active_users_list:
                active_username = self._safe_decode(active_user.get('user', ''))
                active_ip = self._safe_decode(active_user.get('address', ''))
                active_mac = self._safe_decode(active_user.get('mac-address', ''))
                if active_username:
                    active_usernames.add(active_username.lower())
                    # Store the IP address and MAC address for this active user
                    active_users_with_info[active_username.lower()] = {
                        'ip': active_ip,
                        'mac': active_mac if active_mac else 'غير متوفر'
                    }

            # Enhanced filter - search in comment, username, and email
            found_users = []
            for user in users_list:
                username = self._safe_decode(user.get('name', ''))
                comment = self._safe_decode(user.get('comment', ''))
                email = self._safe_decode(user.get('email', ''))

                # Search in multiple fields (prioritize comment but include others)
                if (search_query.lower() in comment.lower() or
                    search_query.lower() in username.lower() or
                    search_query.lower() in email.lower()):
                    # Add active status, IP address, and MAC address to user data
                    user['is_active'] = username.lower() in active_usernames
                    active_info = active_users_with_info.get(username.lower(), {})
                    user['active_ip'] = active_info.get('ip', '')
                    user['active_mac'] = active_info.get('mac', 'غير متوفر')

                    # Always initialize DHCP hostname field
                    user['dhcp_hostname'] = 'غير متوفر'

                    # Try to get hostname from DHCP lease for active users
                    if user['is_active'] and user['active_mac'] != 'غير متوفر':
                        logger.info(f"Looking up DHCP hostname for active user {username} with MAC: {user['active_mac']}")
                        dhcp_info = await self.get_hostname_from_dhcp_lease(user['active_mac'])
                        if dhcp_info:
                            hostname = dhcp_info.get('hostname', '')
                            if hostname and hostname.strip():
                                user['dhcp_hostname'] = hostname
                                logger.info(f"Found DHCP hostname: {user['dhcp_hostname']} for user: {username}")
                            else:
                                logger.info(f"Empty DHCP hostname found for user: {username}")
                        else:
                            logger.info(f"No DHCP hostname found for user: {username}")
                    else:
                        logger.info(f"User {username} is not active or has no MAC address")

                    found_users.append(user)

            total_users = len(found_users)
            start_idx = page * per_page
            end_idx = start_idx + per_page
            page_users = found_users[start_idx:end_idx]

            if total_users > 0:
                escaped_query = self._escape_markdown(search_query)
                message = f"""🔍 **نتائج البحث بالتعليق:** `{escaped_query}`

📊 **إجمالي النتائج:** {total_users} مستخدم
📄 **الصفحة:** {page + 1} من {(total_users - 1) // per_page + 1}

🏨 **مستخدمي الهوت سبوت:**

"""

                # Show results for current page with detailed info
                for i, user in enumerate(page_users, start=start_idx + 1):
                    username = self._escape_markdown(self._safe_decode(user.get('name', 'غير معروف')))
                    profile = self._escape_markdown(self._safe_decode(user.get('profile', 'غير محدد')))
                    comment = self._escape_markdown(self._safe_decode(user.get('comment', 'غير محدد')))
                    disabled = user.get('disabled', 'false')
                    status_icon = "❌" if disabled == 'true' else "✅"
                    active_icon = "🟢" if user.get('is_active', False) else "⚪"

                    # Get data usage information
                    bytes_in = int(user.get('bytes-in', 0))  # Actually Upload (عكس)
                    bytes_out = int(user.get('bytes-out', 0))  # Actually Download (عكس)
                    limit_bytes = int(user.get('limit-bytes-total', 0))  # Total limit
                    total_used = bytes_in + bytes_out

                    # Format using helper function (عكس القيم)
                    upload_text = self._format_bytes(bytes_in)  # bytes-in = رفع
                    download_text = self._format_bytes(bytes_out)  # bytes-out = تحميل
                    total_used_text = self._format_bytes(total_used)

                    # Calculate remaining data
                    if limit_bytes > 0:
                        remaining_bytes = limit_bytes - total_used
                        limit_text = self._format_bytes(limit_bytes)
                        remaining_text = self._format_bytes(remaining_bytes) if remaining_bytes > 0 else "منتهية"
                    else:
                        remaining_text = "غير محدود"
                        limit_text = "غير محدود"

                    message += f"**{i}.** `{username}` {status_icon} {active_icon}\n"
                    message += f"    📋 **الملف:** {profile}\n"
                    message += f"    💬 **التعليق:** {comment}\n"

                    # Always show DHCP hostname field for active users
                    if user.get('is_active', False):
                        hostname = user.get('dhcp_hostname', 'غير متوفر')
                        message += f"    🏠 **اسم الجهاز النشط:** {hostname}\n"

                    # Show IP address if user is active
                    if user.get('is_active', False) and user.get('active_ip'):
                        active_ip = str(user.get('active_ip'))  # IP addresses in backticks don't need escaping
                        message += f"    🌐 **عنوان IP:** `{active_ip}`\n"

                    # Data usage details (عكس التحميل والرفع)
                    message += f"    📊 **استخدام البيانات:**\n"
                    message += f"        📥 **تحميل:** {download_text}\n"
                    message += f"        📤 **رفع:** {upload_text}\n"
                    message += f"        📈 **إجمالي مستخدم:** {total_used_text}\n"
                    message += f"        🎯 **الحد الأقصى:** {limit_text}\n"
                    message += f"        💾 **متبقي:** {remaining_text}\n"

                    # Add email if available
                    email = self._escape_markdown(self._safe_decode(user.get('email', '')))
                    if email and email != 'غير متوفر' and email.strip():
                        message += f"    📧 **البريد:** {email}\n"

                    message += "\n"

                message += """📝 **الرموز:**
• ✅ مفعل | ❌ معطل | 🟢 متصل | ⚪ غير متصل
• 🌐 عنوان IP يظهر فقط للمستخدمين المتصلين حالياً
• 📊 البيانات بالوحدة المناسبة (B, KB, MB, GB)"""
            else:
                escaped_query = self._escape_markdown(search_query)
                message = f"""🔍 **نتائج البحث بالتعليق:** `{escaped_query}`

📭 **لا توجد نتائج**

لم يتم العثور على أي مستخدمين يطابقون البحث في نظام الهوت سبوت."""

        except Exception as e:
            logger.error(f"Hotspot user search by comment failed: {e}")
            error_msg = str(e)
            if "invalid user name or password" in error_msg.lower():
                error_detail = "اسم المستخدم أو كلمة المرور غير صحيحة"
            elif "timeout" in error_msg.lower():
                error_detail = "انتهت مهلة الاتصال"
            elif "connection refused" in error_msg.lower():
                error_detail = "تم رفض الاتصال"
            else:
                error_detail = "خطأ في الاتصال"

            message = f"""❌ **فشل البحث**

{error_detail}"""

        keyboard = []

        if total_users > per_page:
            nav_buttons = []
            if page > 0:
                nav_buttons.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"hotspot_comment_search_page_{page-1}_{search_query}"))

            if end_idx < total_users:
                nav_buttons.append(InlineKeyboardButton("➡️ التالي", callback_data=f"hotspot_comment_search_page_{page+1}_{search_query}"))

            if nav_buttons:
                keyboard.append(nav_buttons)

        action_buttons = [
            InlineKeyboardButton("🔍 بحث جديد", callback_data="hotspot_search_comment"),
            InlineKeyboardButton("🔙 العودة", callback_data="back_to_main")
        ]
        keyboard.append(action_buttons)

        reply_markup = InlineKeyboardMarkup(keyboard)

        self.hotspot_search_results = {
            'users': found_users,
            'search_term': search_query,
            'per_page': per_page
        }

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

    async def handle_hotspot_comment_search_pagination(self, query, callback_data):
        """Handle hotspot comment search pagination"""
        try:
            parts = callback_data.split('_')
            page = int(parts[4])
            search_query = '_'.join(parts[5:])

            await self.search_hotspot_user_by_comment_pagination(query, search_query, page)
        except Exception as e:
            logger.error(f"Error in hotspot comment search pagination: {e}")
            await query.answer("❌ خطأ في التنقل", show_alert=True)

    async def search_hotspot_user_by_comment_pagination(self, query, search_query, page=0, per_page=5):
        """Handle hotspot comment search pagination with detailed data usage info"""
        # Initialize variables to avoid undefined variable errors
        total_users = 0
        end_idx = 0
        found_users = []

        try:
            connection = self.create_connection()
            api = connection.get_api()

            hotspot_users = api.get_resource('/ip/hotspot/user')
            users_list = hotspot_users.get()

            # Get active users
            hotspot_active = api.get_resource('/ip/hotspot/active')
            active_users_list = hotspot_active.get()

            connection.disconnect()

            # Create set of active usernames for quick lookup and store IP addresses and MAC addresses
            active_usernames = set()
            active_users_with_info = {}  # Dictionary to store username -> {IP, MAC} mapping
            for active_user in active_users_list:
                active_username = self._safe_decode(active_user.get('user', ''))
                active_ip = self._safe_decode(active_user.get('address', ''))
                active_mac = self._safe_decode(active_user.get('mac-address', ''))
                if active_username:
                    active_usernames.add(active_username.lower())
                    # Store the IP address and MAC address for this active user
                    active_users_with_info[active_username.lower()] = {
                        'ip': active_ip,
                        'mac': active_mac if active_mac else 'غير متوفر'
                    }

            # Enhanced filter - search in comment, username, and email
            found_users = []
            for user in users_list:
                username = self._safe_decode(user.get('name', ''))
                comment = self._safe_decode(user.get('comment', ''))
                email = self._safe_decode(user.get('email', ''))

                # Search in multiple fields (prioritize comment but include others)
                if (search_query.lower() in comment.lower() or
                    search_query.lower() in username.lower() or
                    search_query.lower() in email.lower()):
                    # Add active status, IP address, and MAC address to user data
                    user['is_active'] = username.lower() in active_usernames
                    active_info = active_users_with_info.get(username.lower(), {})
                    user['active_ip'] = active_info.get('ip', '')
                    user['active_mac'] = active_info.get('mac', 'غير متوفر')

                    # Always initialize DHCP hostname field
                    user['dhcp_hostname'] = 'غير متوفر'

                    # Try to get hostname from DHCP lease for active users
                    if user['is_active'] and user['active_mac'] != 'غير متوفر':
                        logger.info(f"Looking up DHCP hostname for active user {username} with MAC: {user['active_mac']}")
                        dhcp_info = await self.get_hostname_from_dhcp_lease(user['active_mac'])
                        if dhcp_info:
                            hostname = dhcp_info.get('hostname', '')
                            if hostname and hostname.strip():
                                user['dhcp_hostname'] = hostname
                                logger.info(f"Found DHCP hostname: {user['dhcp_hostname']} for user: {username}")
                            else:
                                logger.info(f"Empty DHCP hostname found for user: {username}")
                        else:
                            logger.info(f"No DHCP hostname found for user: {username}")
                    else:
                        logger.info(f"User {username} is not active or has no MAC address")

                    found_users.append(user)

            total_users = len(found_users)
            start_idx = page * per_page
            end_idx = start_idx + per_page
            page_users = found_users[start_idx:end_idx]

            if total_users > 0:
                escaped_query = self._escape_markdown(search_query)
                message = f"""🔍 **نتائج البحث بالتعليق:** `{escaped_query}`

📊 **إجمالي النتائج:** {total_users} مستخدم
📄 **الصفحة:** {page + 1} من {(total_users - 1) // per_page + 1}

🏨 **مستخدمي الهوت سبوت:**

"""

                # Show results for current page with detailed info
                for i, user in enumerate(page_users, start=start_idx + 1):
                    username = self._escape_markdown(self._safe_decode(user.get('name', 'غير معروف')))
                    profile = self._escape_markdown(self._safe_decode(user.get('profile', 'غير محدد')))
                    comment = self._escape_markdown(self._safe_decode(user.get('comment', 'غير محدد')))
                    disabled = user.get('disabled', 'false')
                    status_icon = "❌" if disabled == 'true' else "✅"
                    active_icon = "🟢" if user.get('is_active', False) else "⚪"

                    # Get data usage information
                    bytes_in = int(user.get('bytes-in', 0))  # Actually Upload (عكس)
                    bytes_out = int(user.get('bytes-out', 0))  # Actually Download (عكس)
                    limit_bytes = int(user.get('limit-bytes-total', 0))  # Total limit
                    total_used = bytes_in + bytes_out

                    # Format using helper function (عكس القيم)
                    upload_text = self._format_bytes(bytes_in)  # bytes-in = رفع
                    download_text = self._format_bytes(bytes_out)  # bytes-out = تحميل
                    total_used_text = self._format_bytes(total_used)

                    # Calculate remaining data
                    if limit_bytes > 0:
                        remaining_bytes = limit_bytes - total_used
                        limit_text = self._format_bytes(limit_bytes)
                        remaining_text = self._format_bytes(remaining_bytes) if remaining_bytes > 0 else "منتهية"
                    else:
                        remaining_text = "غير محدود"
                        limit_text = "غير محدود"

                    message += f"**{i}.** `{username}` {status_icon} {active_icon}\n"
                    message += f"    📋 **الملف:** {profile}\n"
                    message += f"    💬 **التعليق:** {comment}\n"

                    # Always show DHCP hostname field for active users
                    if user.get('is_active', False):
                        hostname = user.get('dhcp_hostname', 'غير متوفر')
                        message += f"    🏠 **اسم الجهاز النشط:** {hostname}\n"

                    # Show IP address if user is active
                    if user.get('is_active', False) and user.get('active_ip'):
                        active_ip = str(user.get('active_ip'))  # IP addresses in backticks don't need escaping
                        message += f"    🌐 **عنوان IP:** `{active_ip}`\n"

                    # Data usage details (عكس التحميل والرفع)
                    message += f"    📊 **استخدام البيانات:**\n"
                    message += f"        📥 **تحميل:** {download_text}\n"
                    message += f"        📤 **رفع:** {upload_text}\n"
                    message += f"        📈 **إجمالي مستخدم:** {total_used_text}\n"
                    message += f"        🎯 **الحد الأقصى:** {limit_text}\n"
                    message += f"        💾 **متبقي:** {remaining_text}\n"

                    # Add email if available
                    email = self._escape_markdown(self._safe_decode(user.get('email', '')))
                    if email and email != 'غير متوفر' and email.strip():
                        message += f"    📧 **البريد:** {email}\n"

                    message += "\n"

                message += """📝 **الرموز:**
• ✅ مفعل | ❌ معطل | 🟢 متصل | ⚪ غير متصل
• 🌐 عنوان IP يظهر فقط للمستخدمين المتصلين حالياً
• 📊 البيانات بالوحدة المناسبة (B, KB, MB, GB)"""
            else:
                escaped_query = self._escape_markdown(search_query)
                message = f"""🔍 **نتائج البحث بالتعليق:** `{escaped_query}`

📭 **لا توجد نتائج**

لم يتم العثور على أي مستخدمين يطابقون البحث في نظام الهوت سبوت."""

        except Exception as e:
            logger.error(f"Hotspot comment search pagination failed: {e}")
            message = f"""❌ **فشل البحث**

خطأ في الاتصال"""

        keyboard = []

        if total_users > per_page:
            nav_buttons = []
            if page > 0:
                nav_buttons.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"hotspot_comment_search_page_{page-1}_{search_query}"))

            if end_idx < total_users:
                nav_buttons.append(InlineKeyboardButton("➡️ التالي", callback_data=f"hotspot_comment_search_page_{page+1}_{search_query}"))

            if nav_buttons:
                keyboard.append(nav_buttons)

        action_buttons = [
            InlineKeyboardButton("🔍 بحث جديد", callback_data="hotspot_search_comment"),
            InlineKeyboardButton("🔙 العودة", callback_data="back_to_main")
        ]
        keyboard.append(action_buttons)

        reply_markup = InlineKeyboardMarkup(keyboard)

        await query.edit_message_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

    async def search_userman_user(self, update, search_query):
        """Search for User Manager user (exact match only)"""
        try:
            connection = self.create_connection()
            api = connection.get_api()

            userman_users = api.get_resource('/tool/user-manager/user')
            users_list = userman_users.get()

            connection.disconnect()

            found_user = None
            for user in users_list:
                username = self._safe_decode(user.get('name', ''))
                if username.lower() == search_query.lower():
                    found_user = user
                    break

            if found_user:
                username = self._safe_decode(found_user.get('name', 'غير معروف'))
                disabled = found_user.get('disabled', 'false')
                status = "معطل" if disabled == 'true' else "نشط"

                email = self._safe_decode(found_user.get('email', ''))
                comment = self._safe_decode(found_user.get('comment', ''))

                message = f"""🔒 **البحث:** `{search_query}`

✅ **تم العثور على المستخدم**

• الاسم: `{username}`
• الحالة: `{status}`
• البريد: `{email if email and email != 'غير متوفر' else 'غير محدد'}`
• التعليق: `{comment if comment and comment != 'غير متوفر' else 'غير محدد'}`"""
            else:
                message = f"""🔒 **البحث:** `{search_query}`

❌ **لم يتم العثور على مستخدم**"""

        except Exception as e:
            logger.error(f"User Manager search failed: {e}")
            error_msg = str(e)
            if "invalid user name or password" in error_msg.lower():
                error_detail = "اسم المستخدم أو كلمة المرور غير صحيحة"
            elif "timeout" in error_msg.lower():
                error_detail = "انتهت مهلة الاتصال"
            elif "connection refused" in error_msg.lower():
                error_detail = "تم رفض الاتصال"
            else:
                error_detail = "خطأ في الاتصال"

            message = f"""❌ **فشل البحث**

{error_detail}"""

        keyboard = [
            [
                InlineKeyboardButton("🔍 بحث جديد", callback_data="userman_safe_search"),
                InlineKeyboardButton("🔙 العودة", callback_data="back_to_main")
            ]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )

    async def process_usermanager_safe_search(self, update, search_term):
        """Process safe user manager search - exact username match only"""
        try:
            logger.info(f"Starting safe search for: {search_term}")

            search_term = search_term.strip()
            if not search_term:
                await update.message.reply_text(
                    "❌ **يجب إدخال اسم المستخدم**\n\nأرسل اسم المستخدم الكامل للبحث.",
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
                    ])
                )
                return

            await update.message.reply_text("🔒 **جاري البحث الآمن في User Manager...**")
            logger.info("Sent search progress message")

            try:
                logger.info(f"Connecting to MikroTik using create_connection method")
                connection = self.create_connection()
                api = connection.get_api()
                logger.info("Connected to MikroTik API successfully")

                logger.info("Getting User Manager users...")
                userman_users = api.get_resource('/tool/user-manager/user')
                all_users = userman_users.get()
                logger.info(f"Found {len(all_users)} User Manager users")

                logger.info("Getting User Manager sessions...")
                userman_sessions = api.get_resource('/tool/user-manager/session')
                all_sessions = userman_sessions.get()
                logger.info(f"Found {len(all_sessions)} User Manager sessions")

                connection.disconnect()
                logger.info("Disconnected from MikroTik")

                logger.info(f"Searching for exact match: '{search_term}'")
                matching_user = None
                for user_data in all_users:
                    username = self._safe_decode(user_data.get('username', ''))
                    logger.debug(f"Checking user: '{username}' against '{search_term}'")
                    if username.lower() == search_term.lower():
                        matching_user = user_data
                        logger.info(f"Found matching user: {username}")
                        break

                if not matching_user:
                    logger.info(f"No matching user found for: '{search_term}'")

                if matching_user:
                    username = self._safe_decode(matching_user.get('username', ''))
                    location = self._safe_decode(matching_user.get('location', ''))
                    comment = self._safe_decode(matching_user.get('comment', ''))
                    customer = self._safe_decode(matching_user.get('customer', 'غير محدد'))

                    session_count = 0
                    last_session = 'لم يسجل دخول'
                    total_bytes = 0
                    for session in all_sessions:
                        if self._safe_decode(session.get('user', '')) == username:
                            session_count += 1
                            session_time = self._safe_decode(session.get('till-time', ''))
                            if session_time and session_time != 'غير متوفر':
                                last_session = session_time

                            download = session.get('download', 0)
                            upload = session.get('upload', 0)
                            try:
                                total_bytes += int(download) + int(upload)
                            except (ValueError, TypeError):
                                pass

                    usage_text = self._format_bytes(total_bytes) if total_bytes > 0 else "لا يوجد استخدام"

                    status_icon = "🔥" if session_count > 0 else "💳"
                    status_text = "مستخدم" if session_count > 0 else "لم يستخدم"

                    message = f"""
🔒 **نتيجة البحث الآمن**

✅ **تم العثور على المستخدم**

**معلومات المستخدم:** {status_icon}
• **الاسم:** `{username}`
• **Serial:** `{location}`
• **التعليق:** `{comment}`
• **العميل:** `{customer}`
• **الحالة:** {status_text}
• **عدد الجلسات:** {session_count}
• **إجمالي الاستخدام:** {usage_text}
• **آخر جلسة:** {last_session}

💡 **ملاحظة:** هذا بحث آمن محدود للمستخدمين المحدودين
"""

                else:
                    message = f"""
🔒 **نتيجة البحث الآمن**

❌ **لم يتم العثور على المستخدم**

🔍 **البحث عن:** `{search_term}`

**الأسباب المحتملة:**
• اسم المستخدم غير صحيح
• المستخدم غير موجود في النظام
• خطأ في كتابة الاسم

💡 **تأكد من:**
• كتابة اسم المستخدم كاملاً وبدقة
• عدم وجود مسافات إضافية
• استخدام الأرقام والحروف الصحيحة
"""

                keyboard = [
                    [
                        InlineKeyboardButton("🔍 بحث جديد", callback_data="userman_safe_search"),
                        InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )

            except Exception as e:
                logger.error(f"MikroTik connection error in safe search: {e}")
                await update.message.reply_text(
                    f"❌ **خطأ في الاتصال بـ MikroTik**\n\n`{str(e)}`",
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
                    ])
                )

        except Exception as e:
            logger.error(f"Error in safe search: {e}")
            await update.message.reply_text(
                f"❌ **خطأ في البحث الآمن**\n\n`{str(e)}`",
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=InlineKeyboardMarkup([
                    [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
                ])
            )

    def _format_bytes(self, bytes_value):
        """Format bytes into human readable format"""
        try:
            bytes_value = int(bytes_value)
            if bytes_value < 1024:
                return f"{bytes_value} B"
            elif bytes_value < 1024 * 1024:
                return f"{bytes_value / 1024:.1f} KB"
            elif bytes_value < 1024 * 1024 * 1024:
                return f"{bytes_value / (1024 * 1024):.1f} MB"
            else:
                return f"{bytes_value / (1024 * 1024 * 1024):.1f} GB"
        except (ValueError, TypeError):
            return str(bytes_value)

    async def get_dhcp_leases(self):
        """Get DHCP leases from MikroTik"""
        try:
            connection = self.create_connection()
            api = connection.get_api()

            # Get DHCP leases
            dhcp_leases = api.get_resource('/ip/dhcp-server/lease')
            leases_list = dhcp_leases.get()

            connection.disconnect()

            # Organize leases by MAC address for quick lookup
            leases_by_mac = {}
            leases_with_hostname = 0

            for lease in leases_list:
                mac_address = self._safe_decode(lease.get('mac-address', ''))
                if mac_address and mac_address != 'غير متوفر':
                    # Clean MAC address format (remove any extra characters)
                    mac_clean = mac_address.lower().replace('-', ':').replace(' ', '')

                    hostname = self._safe_decode(lease.get('host-name', ''))
                    lease_info = {
                        'hostname': hostname,
                        'ip': self._safe_decode(lease.get('address', '')),
                        'mac': mac_address,
                        'status': self._safe_decode(lease.get('status', '')),
                        'server': self._safe_decode(lease.get('server', '')),
                        'last_seen': self._safe_decode(lease.get('last-seen', '')),
                        'expires_after': self._safe_decode(lease.get('expires-after', ''))
                    }
                    leases_by_mac[mac_clean] = lease_info

                    # Count leases with hostnames
                    if hostname and hostname != 'غير متوفر' and hostname.strip():
                        leases_with_hostname += 1

            logger.info(f"Retrieved {len(leases_by_mac)} DHCP leases, {leases_with_hostname} with hostnames")

            # Log some sample leases for debugging
            if leases_by_mac:
                sample_count = min(3, len(leases_by_mac))
                sample_leases = list(leases_by_mac.items())[:sample_count]
                for mac, info in sample_leases:
                    logger.debug(f"Sample lease - MAC: {mac}, Hostname: '{info['hostname']}', IP: {info['ip']}")

            return {"success": True, "leases": leases_by_mac}

        except Exception as e:
            logger.error(f"Error getting DHCP leases: {e}")
            return {"success": False, "error": str(e), "leases": {}}

    async def get_hostname_from_dhcp_lease(self, mac_address):
        """Get hostname from DHCP lease using MAC address"""
        try:
            if not mac_address or mac_address == 'غير متوفر':
                logger.debug(f"Invalid MAC address provided: {mac_address}")
                return None

            # Get DHCP leases
            dhcp_result = await self.get_dhcp_leases()
            if not dhcp_result['success']:
                logger.error(f"Failed to get DHCP leases: {dhcp_result['error']}")
                return None

            # Clean MAC address format for comparison
            mac_clean = mac_address.lower().replace('-', ':').replace(' ', '')
            logger.debug(f"Searching for MAC: {mac_clean} in {len(dhcp_result['leases'])} DHCP leases")

            # Look for the MAC address in leases
            leases = dhcp_result['leases']
            if mac_clean in leases:
                lease_info = leases[mac_clean]
                hostname = lease_info.get('hostname', '')
                logger.debug(f"Found lease for MAC {mac_clean}: hostname='{hostname}', ip='{lease_info.get('ip', '')}'")

                if hostname and hostname != 'غير متوفر' and hostname.strip():
                    result = {
                        'hostname': hostname,
                        'ip': lease_info.get('ip', ''),
                        'status': lease_info.get('status', ''),
                        'last_seen': lease_info.get('last_seen', '')
                    }
                    logger.info(f"Returning DHCP info for MAC {mac_clean}: {result}")
                    return result
                else:
                    logger.debug(f"Empty or invalid hostname for MAC {mac_clean}: '{hostname}'")
            else:
                logger.debug(f"MAC {mac_clean} not found in DHCP leases")
                # Log first few MACs for debugging
                sample_macs = list(leases.keys())[:5]
                logger.debug(f"Sample MACs in leases: {sample_macs}")

            return None

        except Exception as e:
            logger.error(f"Error getting hostname from DHCP lease: {e}")
            return None

    async def get_hotspot_active_users_with_mac(self):
        """Get active HotSpot users with their MAC addresses"""
        try:
            connection = self.create_connection()
            api = connection.get_api()

            # Get active users
            hotspot_active = api.get_resource('/ip/hotspot/active')
            active_users_list = hotspot_active.get()

            connection.disconnect()

            # Create dictionary with username -> MAC mapping
            users_with_mac = {}
            for active_user in active_users_list:
                username = self._safe_decode(active_user.get('user', ''))
                mac_address = self._safe_decode(active_user.get('mac-address', ''))
                ip_address = self._safe_decode(active_user.get('address', ''))

                if username:
                    users_with_mac[username.lower()] = {
                        'mac': mac_address if mac_address else 'غير متوفر',
                        'ip': ip_address if ip_address else 'غير متوفر',
                        'username': username
                    }

            logger.info(f"Retrieved {len(users_with_mac)} active HotSpot users with MAC info")
            return {"success": True, "users": users_with_mac}

        except Exception as e:
            logger.error(f"Error getting active HotSpot users with MAC: {e}")
            return {"success": False, "error": str(e), "users": {}}

    async def perform_device_search(self, update, search_query):
        """Perform device search based on query text"""
        await update.message.reply_text(
            f"🔍 **جاري البحث عن:** <code>{search_query}</code>\n\nالرجاء الانتظار...",
            parse_mode=ParseMode.HTML
        )

        try:
            result = await self.discover_network_devices()

            if not result['success']:
                await update.message.reply_text(
                    f"❌ **فشل في البحث**\n\n**الخطأ:** <code>{result['error']}</code>\n\n"
                    "حاول مرة أخرى أو تحقق من الإعدادات.",
                    parse_mode=ParseMode.HTML
                )
                return

            devices = result['devices']
            matching_devices = []

            for device in devices:
                device_ip = self._safe_decode(device.get('ip', ''))
                device_mac = self._safe_decode(device.get('mac', ''))
                device_hostname = self._safe_decode(device.get('hostname', ''))
                device_interface = self._safe_decode(device.get('interface', ''))

                if (self._enhanced_text_search(search_query, device_ip) or
                    self._enhanced_text_search(search_query, device_mac) or
                    self._enhanced_text_search(search_query, device_hostname) or
                    self._enhanced_text_search(search_query, device_interface)):
                    matching_devices.append(device)

            await self.show_device_search_results(update, matching_devices, search_query)

        except Exception as e:
            logger.error(f"Error in device search: {e}")
            await update.message.reply_text(
                f"❌ **فشل في البحث عن الأجهزة**\n\n"
                f"**تفاصيل الخطأ:** <code>{str(e)}</code>\n\n"
                "يرجى المحاولة مرة أخرى أو الاتصال بالمسؤول إذا استمرت المشكلة.",
                parse_mode=ParseMode.HTML
            )

    async def show_device_search_results(self, update, devices, search_query, page=0, per_page=5):
        """Show device search results with pagination"""
        total_devices = len(devices)
        start_idx = page * per_page
        end_idx = start_idx + per_page
        page_devices = devices[start_idx:end_idx]

        if total_devices == 0:
            message = f"""
🔍 <b>نتائج البحث عن:</b> <code>{search_query}</code>

❌ <b>لم يتم العثور على أجهزة</b>

لم يتم العثور على أي أجهزة تطابق البحث.

💡 <b>نصائح للبحث:</b>
• تأكد من كتابة IP أو MAC بشكل صحيح
• جرب البحث بجزء من العنوان
• تأكد من أن الجهاز متصل بالشبكة
            """
        else:
            message = f"""
🔍 <b>نتائج البحث عن:</b> <code>{search_query}</code>

📊 <b>إجمالي النتائج:</b> {total_devices} جهاز
📄 <b>الصفحة:</b> {page + 1} من {(total_devices - 1) // per_page + 1}

"""

            for i, device in enumerate(page_devices, start=start_idx + 1):
                hostname = self._safe_decode(device.get('hostname', ''))
                if not hostname or hostname == 'غير متوفر':
                    hostname = 'غير معروف'

                status = device.get('status', 'unknown')
                status_icon = "🟢" if status in ['reachable', 'up'] else "🔴"

                message += f"""
<b>جهاز {i}:</b> {status_icon}
• <b>الاسم:</b> <code>{hostname}</code>
• <b>IP:</b> <code>{device.get('ip', 'غير محدد')}</code>
• <b>MAC:</b> <code>{device.get('mac', 'غير محدد')}</code>

"""

        keyboard = []

        nav_buttons = []
        if page > 0:
            nav_buttons.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"device_search_page_{page-1}_{search_query}"))

        if end_idx < total_devices:
            nav_buttons.append(InlineKeyboardButton("➡️ التالي", callback_data=f"device_search_page_{page+1}_{search_query}"))

        if nav_buttons:
            keyboard.append(nav_buttons)

        action_buttons = []
        if total_devices > 0:
            action_buttons.append(InlineKeyboardButton("🔄 تحديث", callback_data="device_search_refresh"))
            action_buttons.append(InlineKeyboardButton("🔍 بحث جديد", callback_data="discover_devices"))
        else:
            action_buttons.append(InlineKeyboardButton("🔍 بحث جديد", callback_data="discover_devices"))

        keyboard.append(action_buttons)
        keyboard.append([InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")])

        reply_markup = InlineKeyboardMarkup(keyboard)

        self.device_search_results = {
            'devices': devices,
            'search_term': search_query,
            'per_page': per_page
        }

        if hasattr(update, 'message') and update.message:
            await update.message.reply_text(
                message,
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup
            )
        else:
            await update.edit_message_text(
                message,
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup
            )

    def _enhanced_text_search(self, query_text, target_text):
        """Enhanced text search that handles mixed Arabic/English text properly"""
        if not query_text:
            return False

        if not target_text or target_text == "غير متوفر":
            return False

        try:
            normalized_query = self._normalize_mixed_text(str(query_text))
            normalized_target = self._normalize_mixed_text(str(target_text))

            if not normalized_target or normalized_target == "غير متوفر":
                return False

            query_lower = normalized_query.lower()
            target_lower = normalized_target.lower()

            if query_lower in target_lower:
                return True

            return False

        except Exception as e:
            logger.debug(f"Error in _enhanced_text_search: {e}")
            return False

    async def handle_device_search_pagination(self, query, callback_data):
        """Handle device search pagination"""
        try:
            parts = callback_data.split('_')
            if len(parts) < 4:
                await query.answer("❌ خطأ في البيانات", show_alert=True)
                return

            page = int(parts[3])
            search_term = '_'.join(parts[4:]) if len(parts) > 4 else ""

            if not hasattr(self, 'device_search_results'):
                await query.answer("❌ انتهت صلاحية نتائج البحث", show_alert=True)
                return

            devices = self.device_search_results.get('devices', [])
            per_page = self.device_search_results.get('per_page', 5)

            class FakeUpdate:
                def __init__(self, query):
                    self.query = query

                async def edit_message_text(self, *args, **kwargs):
                    return await self.query.edit_message_text(*args, **kwargs)

            fake_update = FakeUpdate(query)

            await self.show_device_search_results(fake_update, devices, search_term, page, per_page)

        except Exception as e:
            await query.answer(f"❌ خطأ في التنقل: {str(e)}", show_alert=True)

    async def handle_device_search_refresh(self, query):
        """Handle device search refresh button"""
        try:
            if not hasattr(self, 'device_search_results'):
                await query.answer("❌ انتهت صلاحية نتائج البحث", show_alert=True)
                return

            search_term = self.device_search_results.get('search_term', '')

            if not search_term:
                await query.answer("❌ لا يوجد بحث للتحديث", show_alert=True)
                return

            await query.edit_message_text("🔄 <b>جاري تحديث نتائج البحث...</b>")

            result = await self.discover_network_devices()

            if not result['success']:
                await query.edit_message_text(
                    f"❌ <b>فشل في تحديث البحث</b>\n\n<code>{result['error']}</code>",
                    parse_mode=ParseMode.HTML,
                    reply_markup=InlineKeyboardMarkup([
                        [InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")]
                    ])
                )
                return

            devices = result['devices']

            search_term_lower = search_term.lower()
            filtered_devices = []

            for device in devices:
                device_ip = self._safe_decode(device.get('ip', ''))
                device_mac = self._safe_decode(device.get('mac', ''))
                device_hostname = self._safe_decode(device.get('hostname', ''))

                if (self._enhanced_text_search(search_term, device_ip) or
                    self._enhanced_text_search(search_term, device_mac) or
                    self._enhanced_text_search(search_term, device_hostname)):
                    filtered_devices.append(device)

            class FakeUpdate:
                def __init__(self, query):
                    self.query = query

                async def edit_message_text(self, *args, **kwargs):
                    return await self.query.edit_message_text(*args, **kwargs)

            fake_update = FakeUpdate(query)

            await self.show_device_search_results(fake_update, filtered_devices, search_term, page=0)

        except Exception as e:
            await query.answer(f"❌ خطأ في التحديث: {str(e)}", show_alert=True)

    async def handle_hotspot_search_pagination(self, query, callback_data):
        """Handle hotspot search pagination"""
        try:
            parts = callback_data.split('_')
            if len(parts) < 4:
                await query.answer("❌ خطأ في البيانات", show_alert=True)
                return

            page = int(parts[3])
            search_term = '_'.join(parts[4:]) if len(parts) > 4 else ""

            if not hasattr(self, 'hotspot_search_results'):
                await query.answer("❌ انتهت صلاحية نتائج البحث", show_alert=True)
                return

            users = self.hotspot_search_results.get('users', [])
            per_page = self.hotspot_search_results.get('per_page', 5)

            total_users = len(users)
            start_idx = page * per_page
            end_idx = start_idx + per_page
            page_users = users[start_idx:end_idx]

            if total_users > 0:
                message = f"""🔍 **نتائج البحث عن:** `{search_term}`

📊 **إجمالي النتائج:** {total_users} مستخدم
📄 **الصفحة:** {page + 1} من {(total_users - 1) // per_page + 1}

🏨 **مستخدمي الهوت سبوت:**

"""

                for i, user in enumerate(page_users, start=start_idx + 1):
                    username = self._safe_decode(user.get('name', 'غير معروف'))
                    profile = self._safe_decode(user.get('profile', 'غير محدد'))
                    disabled = user.get('disabled', 'false')
                    status_icon = "❌" if disabled == 'true' else "✅"
                    active_icon = "⚪"

                    message += f"**{i}.** `{username}` {status_icon} {active_icon}\n"
                    message += f"    📋 **الملف:** {profile}\n"

                    email = self._safe_decode(user.get('email', ''))
                    if email and email != 'غير متوفر':
                        message += f"    📧 **البريد:** {email}\n"

                    message += "\n"

                message += """📝 **الرموز:**
• ✅ مفعل | ❌ معطل
• 🟢 متصل | ⚪ غير متصل"""

            keyboard = []

            if total_users > per_page:
                nav_buttons = []
                if page > 0:
                    nav_buttons.append(InlineKeyboardButton("⬅️ السابق", callback_data=f"hotspot_search_page_{page-1}_{search_term}"))

                if end_idx < total_users:
                    nav_buttons.append(InlineKeyboardButton("➡️ التالي", callback_data=f"hotspot_search_page_{page+1}_{search_term}"))

                if nav_buttons:
                    keyboard.append(nav_buttons)

            action_buttons = [
                InlineKeyboardButton("🔍 بحث جديد", callback_data="simple_search_hotspot"),
                InlineKeyboardButton("🔙 العودة للقائمة الرئيسية", callback_data="back_to_main")
            ]
            keyboard.append(action_buttons)

            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            await query.answer(f"❌ خطأ في التنقل: {str(e)}", show_alert=True)

    def _safe_decode(self, text):
        """Safely decode text that might be in different encodings with mixed Arabic/English support"""
        import re
        import unicodedata

        if text is None or text == '':
            return "غير متوفر"

        try:
            if isinstance(text, bytes):
                encodings = ['utf-8', 'windows-1256', 'iso-8859-6', 'cp1256', 'latin1']
                for encoding in encodings:
                    try:
                        decoded = text.decode(encoding, errors='ignore')
                        if decoded and decoded.strip():
                            return self._normalize_mixed_text(decoded)
                    except (UnicodeDecodeError, LookupError):
                        continue
                try:
                    return text.decode('utf-8', errors='replace')
                except:
                    return str(text)

            elif isinstance(text, str):
                if ('\\x' in text or re.search(r'\\[A-Fa-f0-9]{2}', text)):
                    try:
                        decoded_text = self._decode_hex_string(text)
                        if decoded_text and decoded_text != text:
                            return self._normalize_mixed_text(decoded_text)
                    except Exception as e:
                        logger.debug(f"Failed to decode hex string: {e}")

                return self._normalize_mixed_text(text)

            else:
                return str(text) if text else "غير متوفر"

        except Exception as e:
            logger.debug(f"Error in _safe_decode: {e}")
            try:
                return str(text) if text else "غير متوفر"
            except:
                return "خطأ في التشفير"

    def _decode_hex_string(self, text):
        """Decode hex-encoded strings like \\xc7\\xcd\\xe3\\xcf to Arabic text"""
        import re

        if not text or not isinstance(text, str):
            return text

        try:
            if '\\x' in text:
                hex_pattern = r'((?:\\x[a-fA-F0-9]{2})+)'
                hex_sequences = re.findall(hex_pattern, text)

                for hex_sequence in hex_sequences:
                    hex_matches = re.findall(r'\\x([a-fA-F0-9]{2})', hex_sequence)
                    if hex_matches:
                        byte_values = [int(hex_val, 16) for hex_val in hex_matches]
                        byte_string = bytes(byte_values)

                        encodings_to_try = [
                            'windows-1256',
                            'cp1256',
                            'iso-8859-6',
                            'utf-8'
                        ]

                        for encoding in encodings_to_try:
                            try:
                                decoded = byte_string.decode(encoding, errors='ignore')
                                if decoded and decoded.strip():
                                    text = text.replace(hex_sequence, decoded)
                                    break
                            except Exception:
                                continue

            elif re.search(r'\\[a-fA-F0-9]{2}', text):
                hex_pattern = r'((?:\\[a-fA-F0-9]{2})+)'
                hex_sequences = re.findall(hex_pattern, text)

                for hex_sequence in hex_sequences:
                    hex_matches = re.findall(r'\\([a-fA-F0-9]{2})', hex_sequence)
                    if hex_matches:
                        byte_values = [int(hex_val, 16) for hex_val in hex_matches]
                        byte_string = bytes(byte_values)

                        encodings_to_try = ['windows-1256', 'cp1256', 'iso-8859-6', 'utf-8']

                        for encoding in encodings_to_try:
                            try:
                                decoded = byte_string.decode(encoding, errors='ignore')
                                if decoded and decoded.strip():
                                    text = text.replace(hex_sequence, decoded)
                                    break
                            except Exception:
                                continue

            return text

        except Exception as e:
            logger.debug(f"Error in _decode_hex_string: {e}")
            return text

    def _normalize_mixed_text(self, text):
        """Normalize mixed Arabic/English text"""
        if not text:
            return "غير متوفر"

        try:
            cleaned = str(text).strip()
            return cleaned if cleaned else "غير متوفر"

        except Exception as e:
            logger.debug(f"Text normalization failed: {e}")
            return str(text) if text else "غير متوفر"

    def _escape_markdown(self, text):
        """Escape special characters for Telegram Markdown"""
        if not text:
            return ""

        try:
            # Convert to string if not already
            text = str(text)

            # Escape special Markdown characters
            special_chars = ['*', '_', '`', '[', ']', '(', ')', '~', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']

            for char in special_chars:
                text = text.replace(char, f'\\{char}')

            return text
        except Exception as e:
            logger.debug(f"Error in _escape_markdown: {e}")
            return str(text) if text else ""

    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE):
        """Handle errors"""
        logger.error(f"Exception while handling an update: {context.error}")

    async def handle_exact_match_search(self, query):
        """Handle exact match search request - show search type options"""
        try:
            message = """🔍 **البحث المتطابق**

اختر نوع البحث في مستخدمي HotSpot:

**🎯 أنواع البحث المتاحة:**
• **بالاسم**: البحث في أسماء المستخدمين
• **بالتعليق**: البحث في تعليقات المستخدمين
• **تنفيذ مباشر**: تنفيذ الأمر مباشرة على السيرفر
• **تصفير مباشر**: تصفير البيانات مباشرة على السيرفر

**📊 المعلومات المعروضة:**
• اسم المستخدم والملف الشخصي
• حالة التفعيل والتعليق
• استخدام البيانات (تحميل/رفع)

**⚙️ الإجراءات المتاحة:**
• تصفير البيانات (إعادة تعيين العدادات)
• إرجاع لكارت (تغيير الاسم وحذف كلمة المرور)
• تنفيذ مباشر للأمر على السيرفر مع إشعار تلقائي
• تصفير مباشر للمستخدمين المعطلين مع إشعار تلقائي"""

            keyboard = [
                [
                    InlineKeyboardButton("👤 البحث بالاسم", callback_data="exact_search_by_name"),
                    InlineKeyboardButton("💬 البحث بالتعليق", callback_data="exact_search_by_comment")
                ],
                [
                    InlineKeyboardButton("⚡ تنفيذ مباشر على السيرفر", callback_data="exact_search_by_exact_comment")
                ],
                [
                    InlineKeyboardButton("🔄 تصفير مباشر على السيرفر", callback_data="direct_reset_server")
                ],
                [InlineKeyboardButton("🔙 العودة", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in handle_exact_match_search: {e}")
            await query.edit_message_text(
                f"❌ **حدث خطأ**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_exact_search_by_name(self, query):
        """Handle exact search by name request"""
        try:
            message = """🔍 **البحث المتطابق بالاسم**

📝 **أدخل اسم المستخدم المراد البحث عنه:**

سيقوم البوت بالبحث في أسماء مستخدمي HotSpot عن التطابق الكامل.

**⚠️ ملاحظات:**
• البحث حساس لحالة الأحرف
• أدخل الاسم كاملاً كما هو مسجل
• سيتم عرض جميع المعلومات والإجراءات المتاحة"""

            keyboard = [[InlineKeyboardButton("❌ إلغاء", callback_data="exact_match_search")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            # Set search mode for name input
            user_id = query.from_user.id
            self.user_search_mode = user_id
            self.search_type = "exact_search_name"

        except Exception as e:
            logger.error(f"Error in handle_exact_search_by_name: {e}")
            await query.edit_message_text(
                f"❌ **حدث خطأ**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_exact_search_by_comment(self, query):
        """Handle exact search by comment request"""
        try:
            message = """🔍 **البحث المتطابق بالتعليق**

📝 **أدخل التعليق المراد البحث عنه:**

سيقوم البوت بالبحث في تعليقات مستخدمي HotSpot عن التطابق الجزئي.

**⚠️ ملاحظات:**
• البحث يجد التطابق الجزئي في التعليقات
• أدخل جزء من التعليق أو التعليق كاملاً
• سيتم عرض جميع المعلومات والإجراءات المتاحة"""

            keyboard = [[InlineKeyboardButton("❌ إلغاء", callback_data="exact_match_search")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            # Set search mode for comment input
            user_id = query.from_user.id
            self.user_search_mode = user_id
            self.search_type = "exact_search_comment"

        except Exception as e:
            logger.error(f"Error in handle_exact_search_by_comment: {e}")
            await query.edit_message_text(
                f"❌ **حدث خطأ**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_exact_search_by_exact_comment(self, query):
        """معالج التنفيذ المباشر للأمر على السيرفر"""
        try:
            message = """⚡ **تنفيذ مباشر على السيرفر**

📝 **أدخل التعليق المراد البحث عنه:**

سيقوم البوت بتنفيذ الأمر مباشرة على السيرفر:
```
:global sq "comment"
:global ssq "chat_id"
:global sssq "token"

:local nameq [/ip hotspot user get [find comment=$sq] name]

/ip hotspot user set [find name=$nameq] comment="" name=$sq profile=card password=""

tool fetch url="https://api.telegram.org/bot$sssq/sendMessage?chat_id=$ssq&text=(Mac=$nameq)(Name=$sq)"
```

**⚡ مميزات التنفيذ المباشر:**
• تنفيذ الأمر مباشرة على MikroTik Server
• إرسال إشعار تلقائي من السيرفر
• سرعة أكبر في التنفيذ
• تطبيق الكود المرجعي كما هو مطلوب

**🔧 الإجراء التلقائي:**
سيتم تنفيذ جميع العمليات تلقائياً بمجرد العثور على المستخدم"""

            keyboard = [[InlineKeyboardButton("❌ إلغاء", callback_data="exact_match_search")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            # تعيين وضع البحث للمستخدم
            user_id = query.from_user.id
            self.user_search_mode = user_id
            self.search_type = "exact_search_exact_comment"

        except Exception as e:
            logger.error(f"Error in handle_exact_search_by_exact_comment: {e}")
            await query.edit_message_text(
                f"❌ **حدث خطأ**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_direct_reset_server(self, query):
        """معالج التصفير المباشر على السيرفر - قائمة الاختيارات"""
        try:
            message = """🔄 **تصفير مباشر على السيرفر**

اختر طريقة البحث للتصفير:

**🎯 أنواع البحث المتاحة:**
• **بالتعليق**: البحث في تعليقات المستخدمين المعطلين
• **بالاسم**: البحث في أسماء المستخدمين المعطلين
• **تصفير خاص**: تصفير متقدم مع معالجة التعليقات والتواريخ

**🔄 العمليات المطبقة:**
• البحث عن المستخدمين المعطلين
• تصفير البيانات لكل مستخدم
• تفعيل المستخدم بعد التصفير
• إرسال إشعار تلقائي من السيرفر

**⭐ التصفير الخاص يتضمن:**
• معالجة متقدمة للتعليقات والتواريخ
• تحديث البريد الإلكتروني
• حذف من الـ Hotspot Host

**⚡ تنفيذ مباشر على MikroTik Server**"""

            keyboard = [
                [
                    InlineKeyboardButton("💬 تصفير بالتعليق", callback_data="direct_reset_by_comment"),
                    InlineKeyboardButton("👤 تصفير بالاسم", callback_data="direct_reset_by_name")
                ],
                [
                    InlineKeyboardButton("⭐ تصفير خاص", callback_data="direct_reset_special")
                ],
                [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in handle_direct_reset_server: {e}")
            await query.edit_message_text(
                f"❌ **حدث خطأ**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_direct_reset_by_comment(self, query):
        """معالج التصفير المباشر بالتعليق"""
        try:
            message = """🔄 **تصفير مباشر بالتعليق**

📝 **أدخل التعليق المراد البحث عنه وتصفيره:**

سيقوم البوت بتنفيذ الأمر مباشرة على السيرفر:
```
:global saq "comment"
:global ssaq "chat_id"
:global sssaq "token"

foreach btonq in=[ip hotspot user find where disabled comment=$saq] do={
local commtnq [ip hotspot user get value-name=comment $btonq ]
local nameq [ip hotspot user get value-name=name $btonq ]

/ip hot user reset $btonq
/ip hot user en $btonq
tool fetch url="https://api.telegram.org/bot$sssaq/sendMessage?chat_id=$ssaq&text=(Reset)(com=$commtnq)(Name=$nameq)"
}
```

**🔄 مميزات التصفير بالتعليق:**
• البحث عن المستخدمين المعطلين بالتعليق المحدد
• تصفير البيانات لكل مستخدم معطل
• تفعيل المستخدم بعد التصفير
• إرسال إشعار تلقائي من السيرفر مع اسم المستخدم والتعليق

**⚠️ ملاحظة مهمة:**
سيتم تصفير جميع المستخدمين المعطلين الذين يحملون نفس التعليق

**مثال:** `عميل جديد`"""

            keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_server")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            # تعيين وضع البحث للمستخدم
            user_id = query.from_user.id
            self.user_search_mode = user_id
            self.search_type = "direct_reset_by_comment"

        except Exception as e:
            logger.error(f"Error in handle_direct_reset_by_comment: {e}")
            await query.edit_message_text(
                f"❌ **حدث خطأ**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_direct_reset_by_name(self, query):
        """معالج التصفير المباشر بالاسم"""
        try:
            message = """🔄 **تصفير مباشر بالاسم**

📝 **أدخل اسم المستخدم المراد تصفيره:**

سيقوم البوت بتنفيذ الأمر مباشرة على السيرفر:
```
:global saq "username"
:global ssaq "chat_id"
:global sssaq "token"

foreach btonq in=[ip hotspot user find where disabled name=$saq] do={
local commtnq [ip hotspot user get value-name=comment $btonq ]
local nameq [ip hotspot user get value-name=name $btonq ]

/ip hot user reset $btonq
/ip hot user en $btonq
tool fetch url="https://api.telegram.org/bot$sssaq/sendMessage?chat_id=$ssaq&text=(Reset)(com=$commtnq)(Name=$nameq)"
}
```

**🔄 مميزات التصفير بالاسم:**
• البحث عن المستخدمين المعطلين بالاسم المحدد
• تصفير البيانات لكل مستخدم معطل
• تفعيل المستخدم بعد التصفير
• إرسال إشعار تلقائي من السيرفر مع اسم المستخدم والتعليق

**⚠️ ملاحظة مهمة:**
سيتم تصفير جميع المستخدمين المعطلين الذين يحملون نفس الاسم

**مثال:** `user123`"""

            keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_server")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            # تعيين وضع البحث للمستخدم
            user_id = query.from_user.id
            self.user_search_mode = user_id
            self.search_type = "direct_reset_by_name"

        except Exception as e:
            logger.error(f"Error in handle_direct_reset_by_name: {e}")
            await query.edit_message_text(
                f"❌ **حدث خطأ**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_direct_reset_special(self, query):
        """معالج التصفير الخاص - قائمة الاختيارات"""
        try:
            message = """⭐ **تصفير خاص على السيرفر**

اختر طريقة البحث للتصفير الخاص:

**🎯 أنواع البحث المتاحة:**
• **بالتعليق**: البحث في تعليقات المستخدمين المعطلين
• **بالاسم**: البحث في أسماء المستخدمين المعطلين

**⭐ مميزات التصفير الخاص:**
• معالجة متقدمة للتعليقات والتواريخ
• تحديث البريد الإلكتروني إلى `<EMAIL>`
• معالجة التواريخ والأوقات من النظام
• حذف من الـ Hotspot Host
• إرسال إشعار مفصل مع التاريخ والوقت

**⚡ تنفيذ مباشر على MikroTik Server**"""

            keyboard = [
                [
                    InlineKeyboardButton("💬 تصفير خاص بالتعليق", callback_data="direct_reset_special_by_comment"),
                    InlineKeyboardButton("👤 تصفير خاص بالاسم", callback_data="direct_reset_special_by_name")
                ],
                [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_server")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in handle_direct_reset_special: {e}")
            await query.edit_message_text(
                f"❌ **حدث خطأ**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def handle_direct_reset_special_by_comment(self, query):
        """معالج التصفير الخاص بالتعليق"""
        try:
            message = """⭐ تصفير خاص بالتعليق

📝 أدخل جزء من التعليق المراد البحث عنه وتصفيره:

سيقوم البوت بالبحث عن جزء من التعليق وتنفيذ التصفير الخاص

🔍 طريقة البحث:
• البحث عن جزء من التعليق وليس التعليق كاملاً
• يبحث في المتغيرات المستخرجة من التعليق
• يطابق الجزء المدخل مع أجزاء التعليقات الموجودة

⭐ العمليات المتقدمة:
• معالجة التواريخ والأوقات من النظام
• تحديث البريد الإلكتروني
• معالجة متقدمة للتعليقات
• تصفير البيانات وتفعيل المستخدم
• حذف من الـ Hotspot Host
• إرسال إشعار مفصل مع التاريخ والوقت

⚠️ ملاحظة مهمة:
سيتم تطبيق التصفير الخاص على جميع المستخدمين المعطلين الذين يحتوي تعليقهم على الجزء المحدد

مثال: 123 (سيبحث عن هذا الرقم في أجزاء التعليقات)"""

            keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_special")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                reply_markup=reply_markup
            )

            # تعيين وضع البحث للمستخدم
            user_id = query.from_user.id
            self.user_search_mode = user_id
            self.search_type = "direct_reset_special_by_comment"

        except Exception as e:
            logger.error(f"Error in handle_direct_reset_special_by_comment: {e}")
            await query.edit_message_text(
                f"❌ حدث خطأ\n\n{str(e)}"
            )

    async def handle_direct_reset_special_by_name(self, query):
        """معالج التصفير الخاص بالاسم"""
        try:
            message = """⭐ **تصفير خاص بالاسم**

📝 **أدخل اسم المستخدم المراد تصفيره:**

سيقوم البوت بتنفيذ التصفير الخاص مباشرة على السيرفر مع:

**⭐ العمليات المتقدمة:**
• معالجة التواريخ والأوقات من النظام
• تحديث البريد الإلكتروني إلى `<EMAIL>`
• معالجة متقدمة للتعليقات (استخراج البيانات)
• تصفير البيانات وتفعيل المستخدم
• حذف من الـ Hotspot Host
• إرسال إشعار مفصل مع التاريخ والوقت

**⚠️ ملاحظة مهمة:**
سيتم تطبيق التصفير الخاص على جميع المستخدمين المعطلين بالاسم المحدد

**مثال:** `user123`"""

            keyboard = [[InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_special")]]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            # تعيين وضع البحث للمستخدم
            user_id = query.from_user.id
            self.user_search_mode = user_id
            self.search_type = "direct_reset_special_by_name"

        except Exception as e:
            logger.error(f"Error in handle_direct_reset_special_by_name: {e}")
            await query.edit_message_text(
                f"❌ **حدث خطأ**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def process_exact_search_by_name(self, update, search_query):
        """Process exact search by name and show results with actions"""
        try:
            # Validate input
            if not search_query or not search_query.strip():
                message = """🔍 **البحث المتطابق بالاسم**

❌ **خطأ في الإدخال**

يجب إدخال اسم المستخدم للبحث.

**مثال صحيح:** `user123`"""

                keyboard = [
                    [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_name")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # Show searching message
            await update.message.reply_text("🔍 **جاري البحث عن المستخدم بالاسم...**")

            # Search for users
            result = await self.find_exact_users_by_name(search_query.strip())

            if not result["success"]:
                message = f"""🔍 **البحث المتطابق بالاسم**

❌ **خطأ في البحث**

{result['error']}"""

                keyboard = [
                    [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_name")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            users = result["users"]

            if not users:
                escaped_query = self._escape_markdown(search_query)
                message = f"""🔍 **البحث المتطابق بالاسم**

🔍 **البحث عن:** `{escaped_query}`

📭 **لا توجد نتائج**

لم يتم العثور على أي مستخدمين بهذا الاسم في HotSpot.

**تأكد من:**
• صحة اسم المستخدم المدخل
• وجود المستخدم في النظام
• كتابة الاسم بالضبط كما هو مسجل"""

                keyboard = [
                    [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_name")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # Display results with actions
            await self.display_exact_search_results(update, users, search_query, "name")

        except Exception as e:
            logger.error(f"Error in process_exact_search_by_name: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

            message = f"""🔍 **البحث المتطابق بالاسم**

❌ **حدث خطأ أثناء البحث**

{str(e)}

يرجى المحاولة مرة أخرى أو التواصل مع المدير."""

            keyboard = [
                [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_name")],
                [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

    async def process_exact_search_by_comment(self, update, search_query):
        """Process exact search by comment and show results with actions"""
        try:
            # Validate input
            if not search_query or not search_query.strip():
                message = """🔍 **البحث المتطابق بالتعليق**

❌ **خطأ في الإدخال**

يجب إدخال التعليق للبحث.

**مثال صحيح:** `عميل جديد`"""

                keyboard = [
                    [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_comment")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # Show searching message
            await update.message.reply_text("🔍 **جاري البحث عن المستخدمين بالتعليق...**")

            # Search for users
            result = await self.find_exact_users_by_comment(search_query.strip())

            if not result["success"]:
                message = f"""🔍 **البحث المتطابق بالتعليق**

❌ **خطأ في البحث**

{result['error']}"""

                keyboard = [
                    [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_comment")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            users = result["users"]

            if not users:
                escaped_query = self._escape_markdown(search_query)
                message = f"""🔍 **البحث المتطابق بالتعليق**

🔍 **البحث عن:** `{escaped_query}`

📭 **لا توجد نتائج**

لم يتم العثور على أي مستخدمين يحتوون على هذا التعليق في HotSpot.

**تأكد من:**
• صحة التعليق المدخل
• وجود مستخدمين بهذا التعليق
• كتابة التعليق بشكل صحيح"""

                keyboard = [
                    [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_comment")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # Display results with actions
            await self.display_exact_search_results(update, users, search_query, "comment")

        except Exception as e:
            logger.error(f"Error in process_exact_search_by_comment: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

            message = f"""🔍 **البحث المتطابق بالتعليق**

❌ **حدث خطأ أثناء البحث**

{str(e)}

يرجى المحاولة مرة أخرى أو التواصل مع المدير."""

            keyboard = [
                [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_comment")],
                [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

    async def find_exact_users_by_name(self, username):
        """Find HotSpot users by exact username match"""
        try:
            connection = self.create_connection()
            api = connection.get_api()

            hotspot_users = api.get_resource('/ip/hotspot/user')
            users_list = hotspot_users.get()

            connection.disconnect()

            matching_users = []

            for user in users_list:
                user_id = user.get('.id', '')
                current_username = self._safe_decode(user.get('name', ''))
                comment = self._safe_decode(user.get('comment', ''))
                password = self._safe_decode(user.get('password', ''))
                profile = self._safe_decode(user.get('profile', ''))
                disabled = user.get('disabled', 'false')

                # Get data usage
                bytes_in = int(user.get('bytes-in', 0))  # Upload
                bytes_out = int(user.get('bytes-out', 0))  # Download
                limit_bytes = int(user.get('limit-bytes-total', 0))

                # Exact match for username
                if current_username.lower() == username.lower():
                    matching_users.append({
                        'id': user_id,
                        'username': current_username,
                        'comment': comment,
                        'password': password,
                        'profile': profile,
                        'disabled': disabled,
                        'bytes_in': bytes_in,
                        'bytes_out': bytes_out,
                        'limit_bytes': limit_bytes,
                        'search_term': username
                    })

            logger.info(f"Found {len(matching_users)} users with exact username '{username}'")
            return {"success": True, "users": matching_users, "search_term": username}

        except Exception as e:
            logger.error(f"Error finding users by exact username '{username}': {e}")
            return {"success": False, "error": str(e), "users": [], "search_term": username}

    async def find_exact_users_by_comment(self, comment_text):
        """Find HotSpot users by comment containing the search text"""
        try:
            connection = self.create_connection()
            api = connection.get_api()

            hotspot_users = api.get_resource('/ip/hotspot/user')
            users_list = hotspot_users.get()

            connection.disconnect()

            matching_users = []

            for user in users_list:
                user_id = user.get('.id', '')
                username = self._safe_decode(user.get('name', ''))
                comment = self._safe_decode(user.get('comment', ''))
                password = self._safe_decode(user.get('password', ''))
                profile = self._safe_decode(user.get('profile', ''))
                disabled = user.get('disabled', 'false')

                # Get data usage
                bytes_in = int(user.get('bytes-in', 0))  # Upload
                bytes_out = int(user.get('bytes-out', 0))  # Download
                limit_bytes = int(user.get('limit-bytes-total', 0))

                # Partial match for comment (case insensitive)
                if comment and comment_text.lower() in comment.lower():
                    matching_users.append({
                        'id': user_id,
                        'username': username,
                        'comment': comment,
                        'password': password,
                        'profile': profile,
                        'disabled': disabled,
                        'bytes_in': bytes_in,
                        'bytes_out': bytes_out,
                        'limit_bytes': limit_bytes,
                        'search_term': comment_text
                    })

            logger.info(f"Found {len(matching_users)} users with comment containing '{comment_text}'")
            return {"success": True, "users": matching_users, "search_term": comment_text}

        except Exception as e:
            logger.error(f"Error finding users by comment '{comment_text}': {e}")
            return {"success": False, "error": str(e), "users": [], "search_term": comment_text}

    async def display_exact_search_results(self, update, users, search_term, search_type):
        """Display exact search results with action buttons"""
        try:
            escaped_term = self._escape_markdown(search_term)
            search_type_text = "الاسم" if search_type == "name" else "التعليق"

            message = f"""🔍 **البحث المتطابق ب{search_type_text}**

🔍 **البحث عن:** `{escaped_term}`

✅ **تم العثور على {len(users)} مستخدم(ين)**

**النتائج:**

"""

            # Store users for actions
            self.exact_search_results = {
                'users': users,
                'search_term': search_term,
                'search_type': search_type
            }

            for i, user in enumerate(users, 1):
                username = self._escape_markdown(user['username'])
                comment = self._escape_markdown(user['comment']) if user['comment'] else 'غير متوفر'
                profile = self._escape_markdown(user['profile'])
                disabled = user.get('disabled', 'false')
                status_icon = "❌" if disabled == 'true' else "✅"

                # Format data usage
                upload_text = self._format_bytes(user['bytes_in'])
                download_text = self._format_bytes(user['bytes_out'])
                total_used = user['bytes_in'] + user['bytes_out']
                total_used_text = self._format_bytes(total_used)

                # Calculate remaining data
                if user['limit_bytes'] > 0:
                    remaining_bytes = user['limit_bytes'] - total_used
                    limit_text = self._format_bytes(user['limit_bytes'])
                    remaining_text = self._format_bytes(remaining_bytes) if remaining_bytes > 0 else "منتهية"
                else:
                    remaining_text = "غير محدود"
                    limit_text = "غير محدود"

                message += f"**{i}.** `{username}` {status_icon}\n"
                message += f"    📋 **الملف:** {profile}\n"
                message += f"    💬 **التعليق:** {comment}\n"
                message += f"    📊 **استخدام البيانات:**\n"
                message += f"        📥 **تحميل:** {download_text}\n"
                message += f"        📤 **رفع:** {upload_text}\n"
                message += f"        📈 **إجمالي:** {total_used_text}\n"
                message += f"        🎯 **الحد الأقصى:** {limit_text}\n"
                message += f"        💾 **متبقي:** {remaining_text}\n\n"

            message += """📝 **الرموز:**
• ✅ مفعل | ❌ معطل
• البيانات بالوحدة المناسبة (B, KB, MB, GB)

⚙️ **الإجراءات المتاحة:**"""

            # Create action buttons for each user
            keyboard = []

            for i, user in enumerate(users):
                user_buttons = [
                    InlineKeyboardButton(f"🔄 تصفير بيانات {i+1}", callback_data=f"exact_reset_data_{i}"),
                    InlineKeyboardButton(f"📋 إرجاع لكارت {i+1}", callback_data=f"exact_restore_card_{i}")
                ]
                keyboard.append(user_buttons)

            # Add navigation buttons
            nav_buttons = [
                InlineKeyboardButton("🔄 بحث جديد", callback_data=f"exact_search_by_{search_type}"),
                InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")
            ]
            keyboard.append(nav_buttons)

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error displaying exact search results: {e}")
            await update.message.reply_text(
                f"❌ **خطأ في عرض النتائج**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def execute_mikrotik_script_command(self, sq, chat_id, token):
        """
        تنفيذ الأمر مباشرة على السيرفر كما هو مطلوب:
        :global sq "comment"
        :global ssq "chat_id"
        :global sssq "token"
        :local nameq [/ip hotspot user get [find comment=$sq] name]
        /ip hotspot user set [find name=$nameq] comment="" name=$sq profile=card password=""
        tool fetch url="https://api.telegram.org/bot$token/sendMessage?chat_id=$chat_id&text=(Mac=$nameq)(Name=$sq)"
        """
        try:
            logger.info(f"🎯 تنفيذ الأمر مباشرة على السيرفر: sq='{sq}', chat_id='{chat_id}'")

            # إنشاء الاتصال
            connection = self.create_connection()
            api = connection.get_api()

            # اختبار الاتصال
            try:
                identity = api.get_resource('/system/identity')
                identity_result = identity.get()
                device_name = identity_result[0].get('name', 'Unknown') if identity_result else 'Unknown'
                logger.info(f"✅ الاتصال نجح مع الجهاز: {device_name}")
            except Exception as test_error:
                logger.error(f"❌ فشل اختبار الاتصال: {test_error}")
                return {
                    "success": False,
                    "error": f"فشل في الاتصال مع MikroTik: {str(test_error)}"
                }

            # الخطوة 1: البحث عن المستخدم والحصول على nameq قبل التنفيذ
            hotspot_users = api.get_resource('/ip/hotspot/user')
            logger.info(f"🔍 البحث عن المستخدم بالتعليق: '{sq}'")

            try:
                all_users = hotspot_users.get()
                logger.info(f"📊 تم الحصول على {len(all_users)} مستخدم من MikroTik")

                found_user = None
                nameq = None

                for user in all_users:
                    user_comment = user.get('comment', '')
                    user_name = user.get('name', '')
                    logger.info(f"🔍 فحص المستخدم: name='{user_name}', comment='{user_comment}', البحث عن: '{sq}'")

                    if user_comment == sq:  # [find comment=$sq]
                        found_user = user
                        nameq = user_name
                        logger.info(f"✅ تم العثور على تطابق! المستخدم: {user_name}")
                        break

                if not found_user or not nameq:
                    logger.warning(f"❌ لم يتم العثور على مستخدم بالتعليق: '{sq}'")
                    connection.disconnect()
                    return {
                        "success": False,
                        "error": f"لم يتم العثور على مستخدم بالتعليق الدقيق '{sq}'\n\nتأكد من:\n• كتابة التعليق بالضبط كما هو مسجل\n• وجود مستخدم بهذا التعليق في النظام"
                    }

                logger.info(f"✅ تم العثور على المستخدم: nameq='{nameq}', sq='{sq}'")

            except Exception as get_error:
                logger.error(f"❌ فشل في الحصول على قائمة المستخدمين: {get_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في الحصول على قائمة المستخدمين: {str(get_error)}"
                }

            # الخطوة 2: تنفيذ الأمر مباشرة على السيرفر باستخدام RouterOS Script
            script_command = f'''
:global sq "{sq}"
:global ssq "{chat_id}"
:global sssq "{token}"

:local nameq [/ip hotspot user get [find comment=$sq] name]

/ip hotspot user set [find name=$nameq] comment="" name=$sq profile=card password=""

tool fetch url="https://api.telegram.org/bot$sssq/sendMessage?chat_id=$ssq&text=(                ( Mac = $nameq )                          ( Name = $sq )                                  ))) &chat_id=$ssq"
'''

            logger.info(f"📜 تنفيذ السكريبت على MikroTik:")
            logger.info(f"Script: {script_command}")

            # تنفيذ السكريبت
            try:
                # استخدام system script و scheduler لتنفيذ الأمر
                system_script = api.get_resource('/system/script')
                system_scheduler = api.get_resource('/system/scheduler')

                # استخدام اسم المبحوث عنه كاسم للسكريپت والجدولة
                script_name = sq
                scheduler_name = f"delete_{sq}"

                # إضافة السكريپت باسم المبحوث عنه
                system_script.add(name=script_name, source=script_command)
                logger.info(f"✅ تم إنشاء السكريپت: {script_name}")

                # إنشاء سكريپت حذف للجدولة
                delete_script_command = f'''
# حذف السكريپت والجدولة
/system script remove [find name="{script_name}"]
/system scheduler remove [find name="{scheduler_name}"]
:log info "تم حذف السكريپت والجدولة: {script_name}"
'''

                # الحصول على الوقت الفعلي من MikroTik Server
                try:
                    system_clock = api.get_resource('/system/clock')
                    clock_info = system_clock.get()[0]
                    mikrotik_time = clock_info.get('time', '')
                    logger.info(f"🕐 وقت MikroTik الحالي: {mikrotik_time}")

                    # تحليل الوقت وإضافة ثانيتين
                    if mikrotik_time:
                        # تحليل الوقت (HH:MM:SS)
                        time_parts = mikrotik_time.split(':')
                        if len(time_parts) == 3:
                            hours = int(time_parts[0])
                            minutes = int(time_parts[1])
                            seconds = int(time_parts[2])

                            # إضافة ثانيتين
                            seconds += 2
                            if seconds >= 60:
                                seconds -= 60
                                minutes += 1
                                if minutes >= 60:
                                    minutes -= 60
                                    hours += 1
                                    if hours >= 24:
                                        hours -= 24

                            # تنسيق الوقت الجديد
                            delete_time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                            logger.info(f"🗑️ وقت الحذف المجدول: {delete_time_str}")
                        else:
                            # في حالة فشل تحليل الوقت، استخدم وقت البوت كبديل
                            logger.warning(f"⚠️ فشل في تحليل وقت MikroTik: {mikrotik_time}")
                            current_time = datetime.now()
                            delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                            delete_time_str = delete_time.strftime("%H:%M:%S")
                    else:
                        # في حالة عدم الحصول على الوقت، استخدم وقت البوت كبديل
                        logger.warning("⚠️ لم يتم الحصول على وقت MikroTik، استخدام وقت البوت")
                        current_time = datetime.now()
                        delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                        delete_time_str = delete_time.strftime("%H:%M:%S")

                except Exception as time_error:
                    logger.error(f"❌ خطأ في الحصول على وقت MikroTik: {time_error}")
                    # استخدام وقت البوت كبديل
                    current_time = datetime.now()
                    delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                    delete_time_str = delete_time.strftime("%H:%M:%S")

                # إنشاء الجدولة بالوقت المحسوب
                system_scheduler.add(
                    name=scheduler_name,
                    **{
                        'start-time': delete_time_str,
                        'interval': '00:00:00',  # تنفيذ مرة واحدة فقط
                        'on-event': delete_script_command
                    }
                )
                logger.info(f"✅ تم إنشاء جدولة الحذف: {scheduler_name} في الوقت {delete_time_str} (وقت MikroTik)")

                # تنفيذ السكريپت الرئيسي
                system_script.call('run', arguments={'.id': script_name})
                logger.info(f"✅ تم تنفيذ السكريپت بنجاح: {script_name}")
                logger.info(f"🗑️ سيتم حذف السكريپت تلقائياً بعد ثانيتين عبر الجدولة: {scheduler_name}")

                connection.disconnect()

                return {
                    "success": True,
                    "sq": sq,
                    "nameq": nameq,  # الاسم الأصلي قبل التغيير
                    "chat_id": chat_id,
                    "token": token,
                    "message": f"تم تنفيذ الأمر بنجاح على السيرفر",
                    "notification": f"(Mac={nameq})(Name={sq})"
                }

            except Exception as script_error:
                logger.error(f"❌ فشل في تنفيذ السكريپت: {script_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في تنفيذ السكريپت على MikroTik: {str(script_error)}"
                }

        except Exception as e:
            logger.error(f"❌ خطأ حرج في تنفيذ الأمر: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"خطأ في تنفيذ الأمر: {str(e)}"
            }

    async def execute_mikrotik_reset_command(self, saq, chat_id, token):
        """
        تنفيذ أمر التصفير مباشرة على السيرفر:
        :global saq "comment"
        :global ssaq "chat_id"
        :global sssaq "token"
        foreach btonq in=[ip hotspot user find where disabled comment=$saq] do={
        local commtnq [ip hotspot user get value-name=comment $btonq ]
        local nameq [ip hotspot user get value-name=name $btonq ]
        /ip hot user reset $btonq
        /ip hot user en $btonq
        tool fetch url="https://api.telegram.org/bot$sssaq/sendMessage?chat_id=$ssaq&text=(Reset)(com=$commtnq)(Name=$nameq)"
        }
        """
        try:
            logger.info(f"🔄 تنفيذ أمر التصفير مباشرة على السيرفر: saq='{saq}', chat_id='{chat_id}'")

            # إنشاء الاتصال
            connection = self.create_connection()
            api = connection.get_api()

            # اختبار الاتصال
            try:
                identity = api.get_resource('/system/identity')
                identity_result = identity.get()
                device_name = identity_result[0].get('name', 'Unknown') if identity_result else 'Unknown'
                logger.info(f"✅ الاتصال نجح مع الجهاز: {device_name}")
            except Exception as test_error:
                logger.error(f"❌ فشل اختبار الاتصال: {test_error}")
                return {
                    "success": False,
                    "error": f"فشل في الاتصال مع MikroTik: {str(test_error)}"
                }

            # الخطوة 1: البحث عن المستخدمين المعطلين بالتعليق المحدد
            hotspot_users = api.get_resource('/ip/hotspot/user')
            logger.info(f"🔍 البحث عن المستخدمين المعطلين بالتعليق: '{saq}'")

            try:
                all_users = hotspot_users.get()
                logger.info(f"📊 تم الحصول على {len(all_users)} مستخدم من MikroTik")

                disabled_users = []
                for user in all_users:
                    user_name = user.get('name', '')
                    user_comment = user.get('comment', '')
                    user_disabled = user.get('disabled', 'false')

                    if user_comment == saq and user_disabled == 'true':
                        disabled_users.append(user)
                        logger.info(f"✅ تم العثور على مستخدم معطل: {user_name} بالتعليق: {user_comment}")

                if not disabled_users:
                    logger.warning(f"❌ لم يتم العثور على مستخدمين معطلين بالتعليق: '{saq}'")
                    connection.disconnect()
                    return {
                        "success": False,
                        "error": f"لم يتم العثور على مستخدمين معطلين بالتعليق '{saq}'\n\nتأكد من:\n• وجود مستخدمين معطلين بهذا التعليق\n• كتابة التعليق بالضبط كما هو مسجل"
                    }

                logger.info(f"✅ تم العثور على {len(disabled_users)} مستخدم معطل")

            except Exception as get_error:
                logger.error(f"❌ فشل في الحصول على قائمة المستخدمين: {get_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في الحصول على قائمة المستخدمين: {str(get_error)}"
                }

            # الخطوة 2: تنفيذ أمر التصفير مباشرة على السيرفر باستخدام RouterOS Script
            script_command = f'''
:global saq "{saq}"
:global ssaq "{chat_id}"
:global sssaq "{token}"

foreach btonq in=[ip hotspot user find where disabled comment=$saq] do={{
local commtnq [ip hotspot user get value-name=comment $btonq ]
local nameq [ip hotspot user get value-name=name $btonq ]

/ip hot user reset $btonq
/ip hot user en $btonq
tool fetch url="https://api.telegram.org/bot$sssaq/sendMessage?chat_id=$ssaq&text=( Reset                ( com = $commtnq )                          ( Name = $nameq )               ))) &chat_id=$ssaq"
}}
'''

            logger.info(f"📜 تنفيذ سكريپت التصفير على MikroTik:")
            logger.info(f"Script: {script_command}")

            # تنفيذ السكريپت
            try:
                # استخدام system script و scheduler لتنفيذ الأمر
                system_script = api.get_resource('/system/script')
                system_scheduler = api.get_resource('/system/scheduler')

                # استخدام التعليق كاسم للسكريپت والجدولة
                script_name = f"reset_comment_{saq}"
                scheduler_name = f"delete_reset_comment_{saq}"

                # إضافة السكريپت باسم التعليق
                system_script.add(name=script_name, source=script_command)
                logger.info(f"✅ تم إنشاء سكريپت التصفير: {script_name}")

                # إنشاء سكريپت حذف للجدولة
                delete_script_command = f'''
# حذف سكريپت التصفير والجدولة
/system script remove [find name="{script_name}"]
/system scheduler remove [find name="{scheduler_name}"]
:log info "تم حذف سكريپت التصفير والجدولة: {script_name}"
'''

                # الحصول على الوقت الفعلي من MikroTik Server وإضافة ثانيتين
                try:
                    system_clock = api.get_resource('/system/clock')
                    clock_info = system_clock.get()[0]
                    mikrotik_time = clock_info.get('time', '')
                    logger.info(f"🕐 وقت MikroTik الحالي: {mikrotik_time}")

                    # تحليل الوقت وإضافة ثانيتين
                    if mikrotik_time:
                        time_parts = mikrotik_time.split(':')
                        if len(time_parts) == 3:
                            hours = int(time_parts[0])
                            minutes = int(time_parts[1])
                            seconds = int(time_parts[2])

                            # إضافة ثانيتين
                            seconds += 2
                            if seconds >= 60:
                                seconds -= 60
                                minutes += 1
                                if minutes >= 60:
                                    minutes -= 60
                                    hours += 1
                                    if hours >= 24:
                                        hours -= 24

                            delete_time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                            logger.info(f"🗑️ وقت حذف سكريپت التصفير: {delete_time_str}")
                        else:
                            # استخدام وقت البوت كبديل
                            current_time = datetime.now()
                            delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                            delete_time_str = delete_time.strftime("%H:%M:%S")
                    else:
                        # استخدام وقت البوت كبديل
                        current_time = datetime.now()
                        delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                        delete_time_str = delete_time.strftime("%H:%M:%S")

                except Exception as time_error:
                    logger.error(f"❌ خطأ في الحصول على وقت MikroTik: {time_error}")
                    current_time = datetime.now()
                    delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                    delete_time_str = delete_time.strftime("%H:%M:%S")

                # إنشاء الجدولة بالوقت المحسوب
                system_scheduler.add(
                    name=scheduler_name,
                    **{
                        'start-time': delete_time_str,
                        'interval': '00:00:00',  # تنفيذ مرة واحدة فقط
                        'on-event': delete_script_command
                    }
                )
                logger.info(f"✅ تم إنشاء جدولة حذف التصفير: {scheduler_name} في الوقت {delete_time_str} (وقت MikroTik)")

                # تنفيذ سكريپت التصفير
                system_script.call('run', arguments={'.id': script_name})
                logger.info(f"✅ تم تنفيذ سكريپت التصفير بنجاح: {script_name}")
                logger.info(f"🗑️ سيتم حذف سكريپت التصفير تلقائياً بعد ثانيتين عبر الجدولة: {scheduler_name}")

                connection.disconnect()

                return {
                    "success": True,
                    "saq": saq,
                    "chat_id": chat_id,
                    "token": token,
                    "disabled_users_count": len(disabled_users),
                    "message": f"تم تنفيذ أمر التصفير بنجاح على السيرفر"
                }

            except Exception as script_error:
                logger.error(f"❌ فشل في تنفيذ سكريپت التصفير: {script_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في تنفيذ سكريپت التصفير على MikroTik: {str(script_error)}"
                }

        except Exception as e:
            logger.error(f"❌ خطأ حرج في تنفيذ أمر التصفير: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"خطأ في تنفيذ أمر التصفير: {str(e)}"
            }

    async def execute_mikrotik_reset_command_by_comment(self, saq, chat_id, token):
        """
        تنفيذ أمر التصفير بالتعليق مباشرة على السيرفر:
        foreach btonq in=[ip hotspot user find where disabled comment=$saq] do={
        local commtnq [ip hotspot user get value-name=comment $btonq ]
        local nameq [ip hotspot user get value-name=name $btonq ]
        /ip hot user reset $btonq
        /ip hot user en $btonq
        tool fetch url="https://api.telegram.org/bot$sssaq/sendMessage?chat_id=$ssaq&text=(Reset)(com=$commtnq)(Name=$nameq)"
        }
        """
        try:
            logger.info(f"🔄 تنفيذ أمر التصفير بالتعليق مباشرة على السيرفر: saq='{saq}', chat_id='{chat_id}'")

            # إنشاء الاتصال
            connection = self.create_connection()
            api = connection.get_api()

            # اختبار الاتصال
            try:
                identity = api.get_resource('/system/identity')
                identity_result = identity.get()
                device_name = identity_result[0].get('name', 'Unknown') if identity_result else 'Unknown'
                logger.info(f"✅ الاتصال نجح مع الجهاز: {device_name}")
            except Exception as test_error:
                logger.error(f"❌ فشل اختبار الاتصال: {test_error}")
                return {
                    "success": False,
                    "error": f"فشل في الاتصال مع MikroTik: {str(test_error)}"
                }

            # البحث عن المستخدمين المعطلين بالتعليق المحدد
            hotspot_users = api.get_resource('/ip/hotspot/user')
            logger.info(f"🔍 البحث عن المستخدمين المعطلين بالتعليق: '{saq}'")

            try:
                all_users = hotspot_users.get()
                logger.info(f"📊 تم الحصول على {len(all_users)} مستخدم من MikroTik")

                disabled_users = []
                for user in all_users:
                    user_name = user.get('name', '')
                    user_comment = user.get('comment', '')
                    user_disabled = user.get('disabled', 'false')

                    if user_comment == saq and user_disabled == 'true':
                        disabled_users.append(user)
                        logger.info(f"✅ تم العثور على مستخدم معطل: {user_name} بالتعليق: {user_comment}")

                if not disabled_users:
                    logger.warning(f"❌ لم يتم العثور على مستخدمين معطلين بالتعليق: '{saq}'")
                    connection.disconnect()
                    return {
                        "success": False,
                        "error": f"لم يتم العثور على مستخدمين معطلين بالتعليق '{saq}'\n\nتأكد من:\n• وجود مستخدمين معطلين بهذا التعليق\n• كتابة التعليق بالضبط كما هو مسجل"
                    }

                logger.info(f"✅ تم العثور على {len(disabled_users)} مستخدم معطل")

            except Exception as get_error:
                logger.error(f"❌ فشل في الحصول على قائمة المستخدمين: {get_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في الحصول على قائمة المستخدمين: {str(get_error)}"
                }

            # تنفيذ أمر التصفير مباشرة على السيرفر باستخدام RouterOS Script
            script_command = f'''
:global saq "{saq}"
:global ssaq "{chat_id}"
:global sssaq "{token}"

foreach btonq in=[ip hotspot user find where disabled comment=$saq] do={{
local commtnq [ip hotspot user get value-name=comment $btonq ]
local nameq [ip hotspot user get value-name=name $btonq ]

/ip hot user reset $btonq
/ip hot user en $btonq
tool fetch url="https://api.telegram.org/bot$sssaq/sendMessage?chat_id=$ssaq&text=( Reset                ( com = $commtnq )                          ( Name = $nameq )               ))) &chat_id=$ssaq"
}}
'''

            logger.info(f"📜 تنفيذ سكريپت التصفير بالتعليق على MikroTik:")

            # تنفيذ السكريپت مع الجدولة
            try:
                system_script = api.get_resource('/system/script')
                system_scheduler = api.get_resource('/system/scheduler')

                script_name = f"reset_comment_{saq}"
                scheduler_name = f"delete_reset_comment_{saq}"

                system_script.add(name=script_name, source=script_command)
                logger.info(f"✅ تم إنشاء سكريپت التصفير بالتعليق: {script_name}")

                # إنشاء جدولة الحذف
                delete_script_command = f'''
/system script remove [find name="{script_name}"]
/system scheduler remove [find name="{scheduler_name}"]
:log info "تم حذف سكريپت التصفير بالتعليق والجدولة: {script_name}"
'''

                # الحصول على وقت MikroTik وإضافة ثانيتين
                try:
                    system_clock = api.get_resource('/system/clock')
                    clock_info = system_clock.get()[0]
                    mikrotik_time = clock_info.get('time', '')

                    if mikrotik_time:
                        time_parts = mikrotik_time.split(':')
                        if len(time_parts) == 3:
                            hours = int(time_parts[0])
                            minutes = int(time_parts[1])
                            seconds = int(time_parts[2])

                            seconds += 2
                            if seconds >= 60:
                                seconds -= 60
                                minutes += 1
                                if minutes >= 60:
                                    minutes -= 60
                                    hours += 1
                                    if hours >= 24:
                                        hours -= 24

                            delete_time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                        else:
                            current_time = datetime.now()
                            delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                            delete_time_str = delete_time.strftime("%H:%M:%S")
                    else:
                        current_time = datetime.now()
                        delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                        delete_time_str = delete_time.strftime("%H:%M:%S")

                except Exception as time_error:
                    logger.error(f"❌ خطأ في الحصول على وقت MikroTik: {time_error}")
                    current_time = datetime.now()
                    delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                    delete_time_str = delete_time.strftime("%H:%M:%S")

                system_scheduler.add(
                    name=scheduler_name,
                    **{
                        'start-time': delete_time_str,
                        'interval': '00:00:00',
                        'on-event': delete_script_command
                    }
                )
                logger.info(f"✅ تم إنشاء جدولة حذف التصفير بالتعليق: {scheduler_name}")

                # تنفيذ السكريپت
                system_script.call('run', arguments={'.id': script_name})
                logger.info(f"✅ تم تنفيذ سكريپت التصفير بالتعليق بنجاح: {script_name}")

                connection.disconnect()

                return {
                    "success": True,
                    "saq": saq,
                    "chat_id": chat_id,
                    "token": token,
                    "disabled_users_count": len(disabled_users),
                    "message": f"تم تنفيذ أمر التصفير بالتعليق بنجاح على السيرفر"
                }

            except Exception as script_error:
                logger.error(f"❌ فشل في تنفيذ سكريپت التصفير بالتعليق: {script_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في تنفيذ سكريپت التصفير بالتعليق على MikroTik: {str(script_error)}"
                }

        except Exception as e:
            logger.error(f"❌ خطأ حرج في تنفيذ أمر التصفير بالتعليق: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"خطأ في تنفيذ أمر التصفير بالتعليق: {str(e)}"
            }

    async def execute_mikrotik_reset_command_by_name(self, saq, chat_id, token):
        """
        تنفيذ أمر التصفير بالاسم مباشرة على السيرفر:
        foreach btonq in=[ip hotspot user find where disabled name=$saq] do={
        local commtnq [ip hotspot user get value-name=comment $btonq ]
        local nameq [ip hotspot user get value-name=name $btonq ]
        /ip hot user reset $btonq
        /ip hot user en $btonq
        tool fetch url="https://api.telegram.org/bot$sssaq/sendMessage?chat_id=$ssaq&text=(Reset)(com=$commtnq)(Name=$nameq)"
        }
        """
        try:
            logger.info(f"🔄 تنفيذ أمر التصفير بالاسم مباشرة على السيرفر: saq='{saq}', chat_id='{chat_id}'")

            # إنشاء الاتصال
            connection = self.create_connection()
            api = connection.get_api()

            # اختبار الاتصال
            try:
                identity = api.get_resource('/system/identity')
                identity_result = identity.get()
                device_name = identity_result[0].get('name', 'Unknown') if identity_result else 'Unknown'
                logger.info(f"✅ الاتصال نجح مع الجهاز: {device_name}")
            except Exception as test_error:
                logger.error(f"❌ فشل اختبار الاتصال: {test_error}")
                return {
                    "success": False,
                    "error": f"فشل في الاتصال مع MikroTik: {str(test_error)}"
                }

            # البحث عن المستخدمين المعطلين بالاسم المحدد
            hotspot_users = api.get_resource('/ip/hotspot/user')
            logger.info(f"🔍 البحث عن المستخدمين المعطلين بالاسم: '{saq}'")

            try:
                all_users = hotspot_users.get()
                logger.info(f"📊 تم الحصول على {len(all_users)} مستخدم من MikroTik")

                disabled_users = []
                for user in all_users:
                    user_name = user.get('name', '')
                    user_disabled = user.get('disabled', 'false')

                    if user_name == saq and user_disabled == 'true':
                        disabled_users.append(user)
                        logger.info(f"✅ تم العثور على مستخدم معطل: {user_name}")

                if not disabled_users:
                    logger.warning(f"❌ لم يتم العثور على مستخدمين معطلين بالاسم: '{saq}'")
                    connection.disconnect()
                    return {
                        "success": False,
                        "error": f"لم يتم العثور على مستخدمين معطلين بالاسم '{saq}'\n\nتأكد من:\n• وجود مستخدمين معطلين بهذا الاسم\n• كتابة الاسم بالضبط كما هو مسجل"
                    }

                logger.info(f"✅ تم العثور على {len(disabled_users)} مستخدم معطل")

            except Exception as get_error:
                logger.error(f"❌ فشل في الحصول على قائمة المستخدمين: {get_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في الحصول على قائمة المستخدمين: {str(get_error)}"
                }

            # تنفيذ أمر التصفير مباشرة على السيرفر باستخدام RouterOS Script
            script_command = f'''
:global saq "{saq}"
:global ssaq "{chat_id}"
:global sssaq "{token}"

foreach btonq in=[ip hotspot user find where disabled name=$saq] do={{
local commtnq [ip hotspot user get value-name=comment $btonq ]
local nameq [ip hotspot user get value-name=name $btonq ]

/ip hot user reset $btonq
/ip hot user en $btonq
tool fetch url="https://api.telegram.org/bot$sssaq/sendMessage?chat_id=$ssaq&text=( Reset                ( com = $commtnq )                          ( Name = $nameq )               ))) &chat_id=$ssaq"
}}
'''

            logger.info(f"📜 تنفيذ سكريپت التصفير بالاسم على MikroTik:")

            # تنفيذ السكريپت مع الجدولة
            try:
                system_script = api.get_resource('/system/script')
                system_scheduler = api.get_resource('/system/scheduler')

                script_name = f"reset_name_{saq}"
                scheduler_name = f"delete_reset_name_{saq}"

                system_script.add(name=script_name, source=script_command)
                logger.info(f"✅ تم إنشاء سكريپت التصفير بالاسم: {script_name}")

                # إنشاء جدولة الحذف
                delete_script_command = f'''
/system script remove [find name="{script_name}"]
/system scheduler remove [find name="{scheduler_name}"]
:log info "تم حذف سكريپت التصفير بالاسم والجدولة: {script_name}"
'''

                # الحصول على وقت MikroTik وإضافة ثانيتين
                try:
                    system_clock = api.get_resource('/system/clock')
                    clock_info = system_clock.get()[0]
                    mikrotik_time = clock_info.get('time', '')

                    if mikrotik_time:
                        time_parts = mikrotik_time.split(':')
                        if len(time_parts) == 3:
                            hours = int(time_parts[0])
                            minutes = int(time_parts[1])
                            seconds = int(time_parts[2])

                            seconds += 2
                            if seconds >= 60:
                                seconds -= 60
                                minutes += 1
                                if minutes >= 60:
                                    minutes -= 60
                                    hours += 1
                                    if hours >= 24:
                                        hours -= 24

                            delete_time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                        else:
                            current_time = datetime.now()
                            delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                            delete_time_str = delete_time.strftime("%H:%M:%S")
                    else:
                        current_time = datetime.now()
                        delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                        delete_time_str = delete_time.strftime("%H:%M:%S")

                except Exception as time_error:
                    logger.error(f"❌ خطأ في الحصول على وقت MikroTik: {time_error}")
                    current_time = datetime.now()
                    delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                    delete_time_str = delete_time.strftime("%H:%M:%S")

                system_scheduler.add(
                    name=scheduler_name,
                    **{
                        'start-time': delete_time_str,
                        'interval': '00:00:00',
                        'on-event': delete_script_command
                    }
                )
                logger.info(f"✅ تم إنشاء جدولة حذف التصفير بالاسم: {scheduler_name}")

                # تنفيذ السكريپت
                system_script.call('run', arguments={'.id': script_name})
                logger.info(f"✅ تم تنفيذ سكريپت التصفير بالاسم بنجاح: {script_name}")

                connection.disconnect()

                return {
                    "success": True,
                    "saq": saq,
                    "chat_id": chat_id,
                    "token": token,
                    "disabled_users_count": len(disabled_users),
                    "message": f"تم تنفيذ أمر التصفير بالاسم بنجاح على السيرفر"
                }

            except Exception as script_error:
                logger.error(f"❌ فشل في تنفيذ سكريپت التصفير بالاسم: {script_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في تنفيذ سكريپت التصفير بالاسم على MikroTik: {str(script_error)}"
                }

        except Exception as e:
            logger.error(f"❌ خطأ حرج في تنفيذ أمر التصفير بالاسم: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"خطأ في تنفيذ أمر التصفير بالاسم: {str(e)}"
            }

    async def execute_mikrotik_special_reset_command_by_comment(self, sawq, chat_id, token):
        """
        تنفيذ أمر التصفير الخاص بالتعليق مباشرة على السيرفر:
        الأمر المتقدم مع معالجة التواريخ والتعليقات
        """
        try:
            logger.info(f"⭐ تنفيذ أمر التصفير الخاص بالتعليق مباشرة على السيرفر: sawq='{sawq}', chat_id='{chat_id}'")

            # إنشاء الاتصال
            connection = self.create_connection()
            api = connection.get_api()

            # اختبار الاتصال
            try:
                identity = api.get_resource('/system/identity')
                identity_result = identity.get()
                device_name = identity_result[0].get('name', 'Unknown') if identity_result else 'Unknown'
                logger.info(f"✅ الاتصال نجح مع الجهاز: {device_name}")
            except Exception as test_error:
                logger.error(f"❌ فشل اختبار الاتصال: {test_error}")
                return {
                    "success": False,
                    "error": f"فشل في الاتصال مع MikroTik: {str(test_error)}"
                }

            # البحث عن المستخدمين المعطلين الذين يحتوي تعليقهم على الجزء المحدد
            hotspot_users = api.get_resource('/ip/hotspot/user')
            logger.info(f"🔍 البحث عن المستخدمين المعطلين الذين يحتوي تعليقهم على: '{sawq}'")

            try:
                all_users = hotspot_users.get()
                logger.info(f"📊 تم الحصول على {len(all_users)} مستخدم من MikroTik")

                disabled_users = []
                for user in all_users:
                    user_name = user.get('name', '')
                    user_comment = user.get('comment', '')
                    user_disabled = user.get('disabled', 'false')

                    # البحث عن جزء من التعليق (numwq أو vomwq)
                    if user_disabled == 'true' and user_comment:
                        # معالجة التعليق لاستخراج الأجزاء
                        try:
                            # البحث عن المحددات في التعليق
                            dash_pos = user_comment.find('-')
                            star_pos = user_comment.find('*')
                            arrow_pos = user_comment.find('>>')
                            dot_pos = user_comment.find('.')

                            # استخراج الأجزاء المختلفة
                            numwq = ""
                            vomwq = ""

                            # استخراج numwq (بين - و *) مع التحقق من صحة المواضع
                            if dash_pos != -1 and star_pos != -1 and star_pos > dash_pos:
                                numwq = user_comment[dash_pos+1:star_pos].strip()

                            # استخراج vomwq (بين >> و .) مع التحقق من صحة المواضع
                            if arrow_pos != -1 and dot_pos != -1 and dot_pos > arrow_pos:
                                vomwq = user_comment[arrow_pos+2:dot_pos].strip()

                            # التحقق من وجود الجزء المطلوب في numwq أو vomwq أو التعليق كاملاً
                            found_in_numwq = sawq in numwq if numwq else False
                            found_in_vomwq = sawq in vomwq if vomwq else False
                            found_in_comment = sawq in user_comment

                            if found_in_numwq or found_in_vomwq or found_in_comment:
                                disabled_users.append(user)
                                match_location = []
                                if found_in_numwq:
                                    match_location.append(f"numwq: '{numwq}'")
                                if found_in_vomwq:
                                    match_location.append(f"vomwq: '{vomwq}'")
                                if found_in_comment and not (found_in_numwq or found_in_vomwq):
                                    match_location.append("التعليق كاملاً")

                                logger.info(f"✅ تم العثور على مستخدم معطل: {user_name}")
                                logger.info(f"   - التعليق: {user_comment}")
                                logger.info(f"   - numwq: '{numwq}', vomwq: '{vomwq}'")
                                logger.info(f"   - تم العثور في: {', '.join(match_location)}")

                        except Exception as parse_error:
                            logger.warning(f"⚠️ فشل في معالجة تعليق المستخدم {user_name}: {parse_error}")
                            # في حالة فشل المعالجة، ابحث في التعليق كاملاً
                            if sawq in user_comment:
                                disabled_users.append(user)
                                logger.info(f"✅ تم العثور على مستخدم معطل (بحث عام): {user_name} بالتعليق: {user_comment}")

                if not disabled_users:
                    logger.warning(f"❌ لم يتم العثور على مستخدمين معطلين يحتوي تعليقهم على: '{sawq}'")

                    # عرض عينة من التعليقات الموجودة للمساعدة في التشخيص
                    sample_comments = []
                    for user in all_users[:5]:  # أول 5 مستخدمين كعينة
                        if user.get('disabled', 'false') == 'true' and user.get('comment', ''):
                            sample_comments.append(user.get('comment', ''))

                    sample_text = ""
                    if sample_comments:
                        sample_text = f"\n\nعينة من التعليقات الموجودة:\n" + "\n".join([f"• {comment}" for comment in sample_comments[:3]])

                    connection.disconnect()
                    return {
                        "success": False,
                        "error": f"لم يتم العثور على مستخدمين معطلين يحتوي تعليقهم على '{sawq}'\n\nتأكد من:\n• وجود مستخدمين معطلين يحتوي تعليقهم على هذا الجزء\n• كتابة الجزء بالضبط كما هو موجود في التعليقات{sample_text}"
                    }

                logger.info(f"✅ تم العثور على {len(disabled_users)} مستخدم معطل")

            except Exception as get_error:
                logger.error(f"❌ فشل في الحصول على قائمة المستخدمين: {get_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في الحصول على قائمة المستخدمين: {str(get_error)}"
                }

            # تنفيذ أمر التصفير الخاص مباشرة على السيرفر باستخدام RouterOS Script
            script_command = f'''
:global sawq "{sawq}"
:global ssawq "{chat_id}"
:global sssawq "{token}"

:global daten [ /system clock get date ]
:global datesun (([pick [sys clock get date ] 4 6]) )
:global datessn (([pick [sys clock get date ] 4 6]) -1 )
:if ($datessn < 10) do={{:set datessn ("0" . $datessn)}}
:global monthun (([pick [sys clock get date ] 0 3]) )
:local et [ /system clock get time ]

foreach btonwq in=[ip hotspot user find where disabled] do={{
    :local userComment [ip hotspot user get value-name=comment $btonwq]
    :local foundMatch false

    :if ([:len $userComment] > 0) do={{
        # معالجة التعليق لاستخراج الأجزاء
        :local dashPos [:find $userComment "-"]
        :local starPos [:find $userComment "*"]
        :local arrowPos [:find $userComment ">>"]
        :local dotPos [:find $userComment "."]

        :local numwq ""
        :local vomwq ""

        # استخراج numwq (بين - و *)
        :if ($dashPos >= 0 && $starPos >= 0 && $starPos > $dashPos) do={{
            :set numwq [:pick $userComment ($dashPos + 1) $starPos]
        }}

        # استخراج vomwq (بين >> و .)
        :if ($arrowPos >= 0 && $dotPos >= 0 && $dotPos > $arrowPos) do={{
            :set vomwq [:pick $userComment ($arrowPos + 2) $dotPos]
        }}

        # التحقق من وجود sawq في numwq أو vomwq أو التعليق كاملاً
        :if ([:find $numwq $sawq] >= 0 || [:find $vomwq $sawq] >= 0 || [:find $userComment $sawq] >= 0) do={{
            :set foundMatch true
        }}
    }}

    :if ($foundMatch) do={{
        :local commtnwq [ip hotspot user get value-name=comment $btonwq]
        :global tjmwq ( "$monthun/$datessn/$[:pick $daten 7 11]" )
        :global tkmwq ( [:find $commtnwq "-" ])
        :global tktmwq ( [:find $commtnwq "*" ])
        :global tkttmwq ( [:find $commtnwq ">>" ])
        :global dotmartkqomwq ([ :find $commtnwq "." ])
        :global vtmwq ([ :pick $commtnwq 0 $tkmwq ])
        :global votmwq ([ :pick $commtnwq $tkmwq $tktmwq ])
        :global vottmwq ([ :pick $commtnwq $tktmwq $tkttmwq ])
        :set votmwq  ""
        :set vottmwq  ""

        :global vomwq ([ :pick $commtnwq $tkttmwq  $dotmartkqomwq ])
        :global voomwq ([ :pick $commtnwq $tktmwq  $tkttmwq ])
        :set voomwq  ""
        /ip hot user set $btonwq email=<EMAIL> comment="$tjmwq - $et  * $vomwq."
        /ip hot user reset $btonwq
        /ip hot user en $btonwq
        /ip hot h remove $btonwq
        tool fetch url="https://api.telegram.org/bot$sssawq/sendMessage?chat_id=$ssawq&text=( Reset ( Mac = $commtnwq ) ( Name = $btonwq ) ( Date = $daten ) (Time = $et ) (comment =$commtnwq) )" &chat_id=$ssawq
    }}
}}
'''

            logger.info(f"📜 تنفيذ سكريپت التصفير الخاص بالتعليق على MikroTik:")

            # تنفيذ السكريپت مع الجدولة
            try:
                system_script = api.get_resource('/system/script')
                system_scheduler = api.get_resource('/system/scheduler')

                # إنشاء أسماء فريدة للسكريپت والجدولة
                import time
                timestamp = str(int(time.time()))
                safe_sawq = "".join(c for c in sawq if c.isalnum())[:10]  # أخذ أول 10 أحرف آمنة
                script_name = f"special_reset_comment_{safe_sawq}_{timestamp}"
                scheduler_name = f"delete_special_reset_comment_{safe_sawq}_{timestamp}"

                system_script.add(name=script_name, source=script_command)
                logger.info(f"✅ تم إنشاء سكريپت التصفير الخاص بالتعليق: {script_name}")

                # إنشاء جدولة الحذف
                delete_script_command = f'''
/system script remove [find name="{script_name}"]
/system scheduler remove [find name="{scheduler_name}"]
:log info "تم حذف سكريپت التصفير الخاص بالتعليق والجدولة: {script_name}"
'''

                # الحصول على وقت MikroTik وإضافة ثانيتين
                try:
                    system_clock = api.get_resource('/system/clock')
                    clock_info = system_clock.get()[0]
                    mikrotik_time = clock_info.get('time', '')

                    if mikrotik_time:
                        time_parts = mikrotik_time.split(':')
                        if len(time_parts) == 3:
                            hours = int(time_parts[0])
                            minutes = int(time_parts[1])
                            seconds = int(time_parts[2])

                            seconds += 2
                            if seconds >= 60:
                                seconds -= 60
                                minutes += 1
                                if minutes >= 60:
                                    minutes -= 60
                                    hours += 1
                                    if hours >= 24:
                                        hours -= 24

                            delete_time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                        else:
                            current_time = datetime.now()
                            delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                            delete_time_str = delete_time.strftime("%H:%M:%S")
                    else:
                        current_time = datetime.now()
                        delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                        delete_time_str = delete_time.strftime("%H:%M:%S")

                except Exception as time_error:
                    logger.error(f"❌ خطأ في الحصول على وقت MikroTik: {time_error}")
                    current_time = datetime.now()
                    delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                    delete_time_str = delete_time.strftime("%H:%M:%S")

                system_scheduler.add(
                    name=scheduler_name,
                    **{
                        'start-time': delete_time_str,
                        'interval': '00:00:00',
                        'on-event': delete_script_command
                    }
                )
                logger.info(f"✅ تم إنشاء جدولة حذف التصفير الخاص بالتعليق: {scheduler_name}")

                # تنفيذ السكريپت
                system_script.call('run', arguments={'.id': script_name})
                logger.info(f"✅ تم تنفيذ سكريپت التصفير الخاص بالتعليق بنجاح: {script_name}")

                connection.disconnect()

                return {
                    "success": True,
                    "saq": sawq,
                    "chat_id": chat_id,
                    "token": token,
                    "disabled_users_count": len(disabled_users),
                    "message": f"تم تنفيذ أمر التصفير الخاص بالتعليق بنجاح على السيرفر"
                }

            except Exception as script_error:
                logger.error(f"❌ فشل في تنفيذ سكريپت التصفير الخاص بالتعليق: {script_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في تنفيذ سكريپت التصفير الخاص بالتعليق على MikroTik: {str(script_error)}"
                }

        except Exception as e:
            logger.error(f"❌ خطأ حرج في تنفيذ أمر التصفير الخاص بالتعليق: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"خطأ في تنفيذ أمر التصفير الخاص بالتعليق: {str(e)}"
            }

    async def execute_mikrotik_special_reset_command_by_name(self, sawq, chat_id, token):
        """
        تنفيذ أمر التصفير الخاص بالاسم مباشرة على السيرفر:
        الأمر المتقدم مع معالجة التواريخ والتعليقات
        """
        try:
            logger.info(f"⭐ تنفيذ أمر التصفير الخاص بالاسم مباشرة على السيرفر: sawq='{sawq}', chat_id='{chat_id}'")

            # إنشاء الاتصال
            connection = self.create_connection()
            api = connection.get_api()

            # اختبار الاتصال
            try:
                identity = api.get_resource('/system/identity')
                identity_result = identity.get()
                device_name = identity_result[0].get('name', 'Unknown') if identity_result else 'Unknown'
                logger.info(f"✅ الاتصال نجح مع الجهاز: {device_name}")
            except Exception as test_error:
                logger.error(f"❌ فشل اختبار الاتصال: {test_error}")
                return {
                    "success": False,
                    "error": f"فشل في الاتصال مع MikroTik: {str(test_error)}"
                }

            # البحث عن المستخدمين المعطلين بالاسم المحدد
            hotspot_users = api.get_resource('/ip/hotspot/user')
            logger.info(f"🔍 البحث عن المستخدمين المعطلين بالاسم: '{sawq}'")

            try:
                all_users = hotspot_users.get()
                logger.info(f"📊 تم الحصول على {len(all_users)} مستخدم من MikroTik")

                disabled_users = []
                for user in all_users:
                    user_name = user.get('name', '')
                    user_disabled = user.get('disabled', 'false')

                    if user_name == sawq and user_disabled == 'true':
                        disabled_users.append(user)
                        logger.info(f"✅ تم العثور على مستخدم معطل: {user_name}")

                if not disabled_users:
                    logger.warning(f"❌ لم يتم العثور على مستخدمين معطلين بالاسم: '{sawq}'")
                    connection.disconnect()
                    return {
                        "success": False,
                        "error": f"لم يتم العثور على مستخدمين معطلين بالاسم '{sawq}'\n\nتأكد من:\n• وجود مستخدمين معطلين بهذا الاسم\n• كتابة الاسم بالضبط كما هو مسجل"
                    }

                logger.info(f"✅ تم العثور على {len(disabled_users)} مستخدم معطل")

            except Exception as get_error:
                logger.error(f"❌ فشل في الحصول على قائمة المستخدمين: {get_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في الحصول على قائمة المستخدمين: {str(get_error)}"
                }

            # تنفيذ أمر التصفير الخاص مباشرة على السيرفر باستخدام RouterOS Script
            script_command = f'''
:global sawq "{sawq}"
:global ssawq "{chat_id}"
:global sssawq "{token}"

:global daten [ /system clock get date ];
:global datesun (([pick [sys clock get date ] 4 6]) )
:global datessn (([pick [sys clock get date ] 4 6]) -1 )
:if ($datessn < 10) do={{:set datessn ("0" . $datessn);}}
:global monthun (([pick [sys clock get date ] 0 3]) )
:local et [ /system clock get time ];

foreach btonwq in=[ip hotspot user find where disabled name=$sawq] do={{
local commtnwq [ip hotspot user get value-name=comment $btonwq ]
:global tjmwq ( "$monthun/$datessn/$[:pick $daten 7 11]" )
:global tkmwq ( [:find $commtnwq "-" ])
:global tktmwq ( [:find $commtnwq "*" ])
:global tkttmwq ( [:find $commtnwq ">>" ])
:global dotmartkqomwq ([ :find $commtnwq "." ])
:global vtmwq ([ :pick $commtnwq 0 $tkmwq ])
:global votmwq ([ :pick $commtnwq $tkmwq $tktmwq ])
:global vottmwq ([ :pick $commtnwq $tktmwq $tkttmwq ])
:set votmwq  ""
:set vottmwq  ""

:global vomwq ([ :pick $commtnwq $tkttmwq  $dotmartkqomwq ])
:global voomwq ([ :pick $commtnwq $tktmwq  $tkttmwq ])
:set voomwq  ""
/ip hot user set $btonwq email=<EMAIL> comment="$tjmwq - $et  * $vomwq."
/ip hot user reset $btonwq
/ip hot user en $btonwq
/ip hot h remove $btonwq
tool fetch url="https://api.telegram.org/bot$sssawq/sendMessage?chat_id=$ssawq&text=( Reset                ( Mac = $commtnwq )                          ( Name = $btonwq )                                                                 ( Date = $daten )                                                                             (Time = $et )                                           (comment =$commtnwq)                         ))) &chat_id=$ssawq"
}}
'''

            logger.info(f"📜 تنفيذ سكريپت التصفير الخاص بالاسم على MikroTik:")

            # تنفيذ السكريپت مع الجدولة
            try:
                system_script = api.get_resource('/system/script')
                system_scheduler = api.get_resource('/system/scheduler')

                script_name = f"special_reset_name_{sawq}"
                scheduler_name = f"delete_special_reset_name_{sawq}"

                system_script.add(name=script_name, source=script_command)
                logger.info(f"✅ تم إنشاء سكريپت التصفير الخاص بالاسم: {script_name}")

                # إنشاء جدولة الحذف
                delete_script_command = f'''
/system script remove [find name="{script_name}"]
/system scheduler remove [find name="{scheduler_name}"]
:log info "تم حذف سكريپت التصفير الخاص بالاسم والجدولة: {script_name}"
'''

                # الحصول على وقت MikroTik وإضافة ثانيتين
                try:
                    system_clock = api.get_resource('/system/clock')
                    clock_info = system_clock.get()[0]
                    mikrotik_time = clock_info.get('time', '')

                    if mikrotik_time:
                        time_parts = mikrotik_time.split(':')
                        if len(time_parts) == 3:
                            hours = int(time_parts[0])
                            minutes = int(time_parts[1])
                            seconds = int(time_parts[2])

                            seconds += 2
                            if seconds >= 60:
                                seconds -= 60
                                minutes += 1
                                if minutes >= 60:
                                    minutes -= 60
                                    hours += 1
                                    if hours >= 24:
                                        hours -= 24

                            delete_time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
                        else:
                            current_time = datetime.now()
                            delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                            delete_time_str = delete_time.strftime("%H:%M:%S")
                    else:
                        current_time = datetime.now()
                        delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                        delete_time_str = delete_time.strftime("%H:%M:%S")

                except Exception as time_error:
                    logger.error(f"❌ خطأ في الحصول على وقت MikroTik: {time_error}")
                    current_time = datetime.now()
                    delete_time = current_time.replace(second=(current_time.second + 2) % 60)
                    delete_time_str = delete_time.strftime("%H:%M:%S")

                system_scheduler.add(
                    name=scheduler_name,
                    **{
                        'start-time': delete_time_str,
                        'interval': '00:00:00',
                        'on-event': delete_script_command
                    }
                )
                logger.info(f"✅ تم إنشاء جدولة حذف التصفير الخاص بالاسم: {scheduler_name}")

                # تنفيذ السكريپت
                system_script.call('run', arguments={'.id': script_name})
                logger.info(f"✅ تم تنفيذ سكريپت التصفير الخاص بالاسم بنجاح: {script_name}")

                connection.disconnect()

                return {
                    "success": True,
                    "saq": sawq,
                    "chat_id": chat_id,
                    "token": token,
                    "disabled_users_count": len(disabled_users),
                    "message": f"تم تنفيذ أمر التصفير الخاص بالاسم بنجاح على السيرفر"
                }

            except Exception as script_error:
                logger.error(f"❌ فشل في تنفيذ سكريپت التصفير الخاص بالاسم: {script_error}")
                connection.disconnect()
                return {
                    "success": False,
                    "error": f"فشل في تنفيذ سكريپت التصفير الخاص بالاسم على MikroTik: {str(script_error)}"
                }

        except Exception as e:
            logger.error(f"❌ خطأ حرج في تنفيذ أمر التصفير الخاص بالاسم: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"خطأ في تنفيذ أمر التصفير الخاص بالاسم: {str(e)}"
            }

    async def apply_reference_code_logic(self, sq):
        """
        تطبيق مباشر للكود المرجعي:
        :global sq "comment"
        :local nameq [/ip hotspot user get [find comment=$sq] name]
        /ip hotspot user set [find name=$nameq] comment="" name=$sq profile=card password=""
        tool fetch url="https://api.telegram.org/bot$token/sendMessage?chat_id=$chat_id&text=(Mac=$nameq)(Name=$sq)"
        """
        try:
            logger.info(f"🎯 تطبيق الكود المرجعي: sq='{sq}'")

            # إنشاء الاتصال مع تسجيل مفصل
            logger.info(f"🔗 إنشاء اتصال مع MikroTik: {self.actual_host}:{self.mikrotik_port}")
            connection = self.create_connection()
            api = connection.get_api()

            # اختبار الاتصال
            try:
                identity = api.get_resource('/system/identity')
                identity_result = identity.get()
                device_name = identity_result[0].get('name', 'Unknown') if identity_result else 'Unknown'
                logger.info(f"✅ الاتصال نجح مع الجهاز: {device_name}")
            except Exception as test_error:
                logger.error(f"❌ فشل اختبار الاتصال: {test_error}")
                return {
                    "success": False,
                    "error": f"فشل في الاتصال مع MikroTik: {str(test_error)}"
                }

            hotspot_users = api.get_resource('/ip/hotspot/user')
            logger.info(f"✅ تم الحصول على مورد hotspot users")

            # الخطوة 1: تطبيق [find comment=$sq]
            logger.info(f"🔍 تطبيق [find comment=$sq] للبحث عن: '{sq}'")

            try:
                all_users = hotspot_users.get()
                logger.info(f"📊 تم الحصول على {len(all_users)} مستخدم من MikroTik")

                # تسجيل عينة من التعليقات الموجودة للمساعدة في التشخيص
                sample_comments = []
                for i, user in enumerate(all_users[:5]):  # أول 5 مستخدمين
                    comment = user.get('comment', '')
                    name = user.get('name', '')
                    sample_comments.append(f"User {i+1}: name='{name}', comment='{comment}'")

                logger.info(f"📋 عينة من المستخدمين:")
                for sample in sample_comments:
                    logger.info(f"   {sample}")

            except Exception as get_error:
                logger.error(f"❌ فشل في الحصول على قائمة المستخدمين: {get_error}")
                return {
                    "success": False,
                    "error": f"فشل في الحصول على قائمة المستخدمين: {str(get_error)}"
                }

            found_user = None

            for user in all_users:
                user_comment = user.get('comment', '')
                user_name = user.get('name', '')
                logger.info(f"🔍 فحص المستخدم: name='{user_name}', comment='{user_comment}', البحث عن: '{sq}'")

                if user_comment == sq:  # [find comment=$sq]
                    found_user = user
                    logger.info(f"✅ تم العثور على تطابق! المستخدم: {user_name}")
                    break

            if not found_user:
                logger.warning(f"❌ لم يتم العثور على مستخدم بالتعليق: '{sq}'")
                return {
                    "success": False,
                    "error": f"لم يتم العثور على مستخدم بالتعليق الدقيق '{sq}'\n\nتأكد من:\n• كتابة التعليق بالضبط كما هو مسجل\n• وجود مستخدم بهذا التعليق في النظام"
                }

            # الخطوة 2: استخراج nameq
            # :local nameq [/ip hotspot user get [find comment=$sq] name]
            user_id = found_user.get('.id', '')
            nameq = found_user.get('name', '')  # :local nameq

            logger.info(f"✅ تم العثور على المستخدم: nameq='{nameq}', sq='{sq}'")
            logger.info(f"🔄 تطبيق: /ip hotspot user set [find name=$nameq] comment=\"\" name=$sq profile=card password=\"\"")

            # الخطوة 3: تطبيق التغييرات
            # /ip hotspot user set [find name=$nameq] comment="" name=$sq profile=card password=""
            success = False

            try:
                # Method 1: تطبيق مباشر للكود المرجعي
                logger.info(f"METHOD 1: تطبيق مباشر للكود المرجعي")
                logger.info(f"🔧 معلومات الاتصال: Host={self.actual_host}, User={self.mikrotik_username}")

                # تسجيل البيانات قبل التحديث
                logger.info(f"📋 البيانات قبل التحديث:")
                logger.info(f"   user_id: {user_id}")
                logger.info(f"   nameq (الاسم الحالي): {nameq}")
                logger.info(f"   sq (الاسم الجديد): {sq}")

                update_data = {
                    '.id': user_id,           # [find name=$nameq]
                    'comment': '',            # comment=""
                    'name': sq,               # name=$sq
                    'profile': 'card',        # profile=card
                    'password': ''            # password=""
                }

                logger.info(f"📤 تطبيق التحديث: {update_data}")

                # تطبيق التحديث مع معالجة أخطاء مفصلة
                try:
                    result = hotspot_users.set(**update_data)
                    logger.info(f"✅ نتيجة التحديث من MikroTik: {result}")
                    logger.info(f"✅ تم إرسال أمر التحديث بنجاح إلى MikroTik")
                except Exception as set_error:
                    logger.error(f"❌ خطأ في تطبيق التحديث: {set_error}")
                    logger.error(f"❌ نوع الخطأ: {type(set_error)}")
                    raise set_error

                # التحقق من التغييرات
                time.sleep(0.5)  # انتظار قصير لتطبيق التغييرات
                updated_user_list = hotspot_users.get(**{'.id': user_id})
                if updated_user_list:
                    updated_user = updated_user_list[0] if isinstance(updated_user_list, list) else updated_user_list
                    new_name = updated_user.get('name', '')
                    new_password = updated_user.get('password', '')
                    new_comment = updated_user.get('comment', '')
                    new_profile = updated_user.get('profile', '')

                    logger.info(f"📊 التحقق - Name: '{new_name}', Password: {'***' if new_password else 'Empty'}, Comment: '{new_comment}', Profile: '{new_profile}'")

                    # فحص جميع التغييرات
                    name_ok = (new_name == sq)
                    password_ok = (new_password == '')
                    comment_ok = (new_comment == '')
                    profile_ok = (new_profile == 'card')

                    if name_ok and password_ok and comment_ok and profile_ok:
                        logger.info(f"✅ تم التحقق من جميع التغييرات بنجاح")
                        success = True
                    else:
                        logger.error(f"❌ فشل التحقق - Name: {name_ok}, Password: {password_ok}, Comment: {comment_ok}, Profile: {profile_ok}")

            except Exception as direct_error:
                logger.error(f"❌ فشل التطبيق المباشر: {direct_error}")
                logger.error(f"❌ نوع الخطأ: {type(direct_error)}")

                # Method 2: تطبيق تدريجي كطريقة بديلة
                logger.info(f"METHOD 2: محاولة التطبيق التدريجي")
                try:
                    # خطوة 1: حذف التعليق
                    logger.info(f"خطوة 1: حذف التعليق")
                    hotspot_users.set(**{'.id': user_id, 'comment': ''})
                    time.sleep(0.3)

                    # خطوة 2: حذف كلمة المرور
                    logger.info(f"خطوة 2: حذف كلمة المرور")
                    hotspot_users.set(**{'.id': user_id, 'password': ''})
                    time.sleep(0.3)

                    # خطوة 3: تغيير الاسم
                    logger.info(f"خطوة 3: تغيير الاسم من '{nameq}' إلى '{sq}'")
                    hotspot_users.set(**{'.id': user_id, 'name': sq})
                    time.sleep(0.3)

                    # خطوة 4: تعيين الملف الشخصي
                    logger.info(f"خطوة 4: تعيين الملف الشخصي إلى 'card'")
                    hotspot_users.set(**{'.id': user_id, 'profile': 'card'})
                    time.sleep(0.5)

                    logger.info(f"✅ تم التطبيق التدريجي بنجاح")

                    # التحقق من التغييرات
                    updated_user_list = hotspot_users.get(**{'.id': user_id})
                    if updated_user_list:
                        updated_user = updated_user_list[0] if isinstance(updated_user_list, list) else updated_user_list
                        new_name = updated_user.get('name', '')
                        new_password = updated_user.get('password', '')
                        new_comment = updated_user.get('comment', '')
                        new_profile = updated_user.get('profile', '')

                        logger.info(f"📊 التحقق بعد التطبيق التدريجي - Name: '{new_name}', Password: {'***' if new_password else 'Empty'}, Comment: '{new_comment}', Profile: '{new_profile}'")

                        # فحص جميع التغييرات
                        name_ok = (new_name == sq)
                        password_ok = (new_password == '')
                        comment_ok = (new_comment == '')
                        profile_ok = (new_profile == 'card')

                        if name_ok and password_ok and comment_ok and profile_ok:
                            logger.info(f"✅ تم التحقق من جميع التغييرات بنجاح (التطبيق التدريجي)")
                            success = True
                        else:
                            logger.error(f"❌ فشل التحقق (التطبيق التدريجي) - Name: {name_ok}, Password: {password_ok}, Comment: {comment_ok}, Profile: {profile_ok}")

                except Exception as step_error:
                    logger.error(f"❌ فشل التطبيق التدريجي أيضاً: {step_error}")
                    return {
                        "success": False,
                        "error": f"فشل في تطبيق التغييرات (كلا الطريقتين): المباشر: {str(direct_error)}, التدريجي: {str(step_error)}"
                    }

            connection.disconnect()

            if success:
                # الخطوة 4: إرسال الإشعار
                # tool fetch url="https://api.telegram.org/bot$token/sendMessage?chat_id=$chat_id&text=(Mac=$nameq)(Name=$sq)"
                notification_text = f"(Mac={nameq})(Name={sq})"
                logger.info(f"📱 إرسال الإشعار: {notification_text}")

                logger.info(f"🎉 تم تطبيق الكود المرجعي بنجاح: nameq='{nameq}' → sq='{sq}'")

                return {
                    "success": True,
                    "nameq": nameq,  # الاسم الأصلي
                    "sq": sq,        # التعليق المبحوث عنه (الاسم الجديد)
                    "notification": notification_text
                }
            else:
                return {
                    "success": False,
                    "error": "فشل في تطبيق التغييرات على المستخدم"
                }

        except Exception as e:
            logger.error(f"❌ خطأ حرج في تطبيق الكود المرجعي: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return {
                "success": False,
                "error": f"خطأ في تطبيق الكود المرجعي: {str(e)}"
            }



    async def process_exact_search_by_exact_comment(self, update, search_query):
        """
        تطبيق مباشر للكود المرجعي:
        :global sq "comment"
        :local nameq [/ip hotspot user get [find comment=$sq] name]
        /ip hotspot user set [find name=$nameq] comment="" name=$sq profile=card password=""
        tool fetch url="https://api.telegram.org/bot$token/sendMessage?chat_id=$chat_id&text=(Mac=$nameq)(Name=$sq)"
        """
        try:
            # التحقق من صحة الإدخال
            if not search_query or not search_query.strip():
                message = """🎯 **البحث الدقيق بالتعليق**

❌ **خطأ في الإدخال**

يرجى إدخال التعليق المراد البحث عنه.

**مثال صحيح:** `عميل جديد`"""

                keyboard = [
                    [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_exact_comment")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # عرض رسالة المعالجة
            await update.message.reply_text("🎯 **جاري تنفيذ الأمر مباشرة على السيرفر...**")

            # الحصول على معلومات البوت والمحادثة
            chat_id = str(update.effective_chat.id)

            # تنفيذ الأمر مباشرة على السيرفر
            result = await self.execute_mikrotik_script_command(search_query.strip(), chat_id, self.bot_token)

            if not result["success"]:
                message = f"""🎯 **البحث الدقيق بالتعليق**

❌ **فشلت العملية!**

{result['error']}"""

                keyboard = [
                    [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_exact_comment")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # رسالة النجاح
            escaped_sq = self._escape_markdown(result['sq'])
            escaped_nameq = self._escape_markdown(result['nameq'])
            escaped_chat_id = self._escape_markdown(result['chat_id'])

            message = f"""🎯 **البحث المتطابق بالتعليق**

✅ **تم تنفيذ الأمر مباشرة على السيرفر بنجاح!**

📊 **المعلومات المستخرجة:**
• **sq (التعليق المبحوث):** `{escaped_sq}`
• **nameq (الاسم الأصلي):** `{escaped_nameq}`
• **ssq (معرف المحادثة):** `{escaped_chat_id}`

🔄 **العمليات المطبقة على السيرفر:**
• **[find comment=$sq]:** تم العثور على المستخدم `{escaped_nameq}`
• **comment="":** تم حذف التعليق
• **name=$sq:** تم تغيير الاسم من `{escaped_nameq}` إلى `{escaped_sq}`
• **profile=card:** تم تعيين الملف الشخصي إلى 'card'
• **password="":** تم حذف كلمة المرور

📱 **الإشعار المرسل من السيرفر:** `{result['notification']}`

⚡ **تم التنفيذ مباشرة على MikroTik Server**

⏰ **وقت العملية:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            keyboard = [
                [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_exact_comment")],
                [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")],
                [InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            logger.info(f"تم تنفيذ الأمر مباشرة على السيرفر بنجاح: sq='{result['sq']}', nameq='{result['nameq']}', chat_id='{result['chat_id']}'")

        except Exception as e:
            logger.error(f"Error in process_exact_search_by_exact_comment: {e}")
            message = f"""🎯 **البحث الدقيق بالتعليق**

❌ **حدث خطأ غير متوقع**

{str(e)}

يرجى المحاولة مرة أخرى أو التواصل مع المدير."""

            keyboard = [
                [InlineKeyboardButton("🔄 بحث جديد", callback_data="exact_search_by_exact_comment")],
                [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

    async def process_direct_reset_server(self, update, search_query):
        """معالج التصفير المباشر على السيرفر"""
        try:
            # التحقق من صحة الإدخال
            if not search_query or not search_query.strip():
                message = """🔄 **تصفير مباشر على السيرفر**

❌ **خطأ في الإدخال**

يرجى إدخال التعليق المراد البحث عنه وتصفيره.

**مثال صحيح:** `عميل جديد`"""

                keyboard = [
                    [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_server")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # عرض رسالة المعالجة
            await update.message.reply_text("🔄 **جاري تنفيذ التصفير مباشرة على السيرفر...**")

            # الحصول على معلومات البوت والمحادثة
            chat_id = str(update.effective_chat.id)

            # تنفيذ أمر التصفير مباشرة على السيرفر
            result = await self.execute_mikrotik_reset_command(search_query.strip(), chat_id, self.bot_token)

            if not result["success"]:
                message = f"""🔄 **تصفير مباشر على السيرفر**

❌ **فشلت العملية!**

{result['error']}"""

                keyboard = [
                    [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_server")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # رسالة النجاح
            escaped_saq = self._escape_markdown(result['saq'])
            escaped_chat_id = self._escape_markdown(result['chat_id'])

            message = f"""🔄 **تصفير مباشر على السيرفر**

✅ **تم تنفيذ أمر التصفير مباشرة على السيرفر بنجاح!**

📊 **المعلومات المستخدمة:**
• **saq (التعليق المبحوث):** `{escaped_saq}`
• **ssaq (معرف المحادثة):** `{escaped_chat_id}`
• **sssaq (رمز البوت):** محجوب للأمان

🔄 **العمليات المطبقة على السيرفر:**
• **البحث:** عن المستخدمين المعطلين بالتعليق `{escaped_saq}`
• **التصفير:** `ip hot user reset` لكل مستخدم معطل
• **التفعيل:** `ip hot user en` لكل مستخدم بعد التصفير
• **الإشعار:** إرسال تلقائي من السيرفر لكل مستخدم مع اسمه وتعليقه

⚡ **تم التنفيذ مباشرة على MikroTik Server**

⏰ **وقت العملية:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            keyboard = [
                [InlineKeyboardButton("🔄 تصفير جديد", callback_data="direct_reset_server")],
                [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")],
                [InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            logger.info(f"تم تنفيذ التصفير مباشرة على السيرفر بنجاح: saq='{result['saq']}', chat_id='{result['chat_id']}'")

        except Exception as e:
            logger.error(f"Error in process_direct_reset_server: {e}")
            message = f"""🔄 **تصفير مباشر على السيرفر**

❌ **حدث خطأ غير متوقع**

{str(e)}

يرجى المحاولة مرة أخرى أو التواصل مع المدير."""

            keyboard = [
                [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_server")],
                [InlineKeyboardButton("🔙 العودة", callback_data="exact_match_search")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

    async def process_direct_reset_by_comment(self, update, search_query):
        """معالج التصفير المباشر بالتعليق"""
        try:
            # التحقق من صحة الإدخال
            if not search_query or not search_query.strip():
                message = """🔄 **تصفير مباشر بالتعليق**

❌ **خطأ في الإدخال**

يرجى إدخال التعليق المراد البحث عنه وتصفيره.

**مثال صحيح:** `عميل جديد`"""

                keyboard = [
                    [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_by_comment")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_server")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # عرض رسالة المعالجة
            await update.message.reply_text("🔄 **جاري تنفيذ التصفير بالتعليق مباشرة على السيرفر...**")

            # الحصول على معلومات البوت والمحادثة
            chat_id = str(update.effective_chat.id)

            # تنفيذ أمر التصفير بالتعليق مباشرة على السيرفر
            result = await self.execute_mikrotik_reset_command_by_comment(search_query.strip(), chat_id, self.bot_token)

            if not result["success"]:
                message = f"""🔄 **تصفير مباشر بالتعليق**

❌ **فشلت العملية!**

{result['error']}"""

                keyboard = [
                    [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_by_comment")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_server")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # رسالة النجاح
            escaped_saq = self._escape_markdown(result['saq'])
            escaped_chat_id = self._escape_markdown(result['chat_id'])

            message = f"""🔄 **تصفير مباشر بالتعليق**

✅ **تم تنفيذ أمر التصفير بالتعليق مباشرة على السيرفر بنجاح!**

📊 **المعلومات المستخدمة:**
• **saq (التعليق المبحوث):** `{escaped_saq}`
• **ssaq (معرف المحادثة):** `{escaped_chat_id}`
• **sssaq (رمز البوت):** محجوب للأمان

🔄 **العمليات المطبقة على السيرفر:**
• **البحث:** عن المستخدمين المعطلين بالتعليق `{escaped_saq}`
• **التصفير:** `ip hot user reset` لكل مستخدم معطل
• **التفعيل:** `ip hot user en` لكل مستخدم بعد التصفير
• **الإشعار:** إرسال تلقائي من السيرفر لكل مستخدم مع اسمه وتعليقه

⚡ **تم التنفيذ مباشرة على MikroTik Server**

⏰ **وقت العملية:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            keyboard = [
                [InlineKeyboardButton("🔄 تصفير جديد", callback_data="direct_reset_by_comment")],
                [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_server")],
                [InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            logger.info(f"تم تنفيذ التصفير بالتعليق مباشرة على السيرفر بنجاح: saq='{result['saq']}', chat_id='{result['chat_id']}'")

        except Exception as e:
            logger.error(f"Error in process_direct_reset_by_comment: {e}")
            message = f"""🔄 **تصفير مباشر بالتعليق**

❌ **حدث خطأ غير متوقع**

{str(e)}

يرجى المحاولة مرة أخرى أو التواصل مع المدير."""

            keyboard = [
                [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_by_comment")],
                [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_server")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

    async def process_direct_reset_by_name(self, update, search_query):
        """معالج التصفير المباشر بالاسم"""
        try:
            # التحقق من صحة الإدخال
            if not search_query or not search_query.strip():
                message = """🔄 **تصفير مباشر بالاسم**

❌ **خطأ في الإدخال**

يرجى إدخال اسم المستخدم المراد تصفيره.

**مثال صحيح:** `user123`"""

                keyboard = [
                    [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_by_name")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_server")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # عرض رسالة المعالجة
            await update.message.reply_text("🔄 **جاري تنفيذ التصفير بالاسم مباشرة على السيرفر...**")

            # الحصول على معلومات البوت والمحادثة
            chat_id = str(update.effective_chat.id)

            # تنفيذ أمر التصفير بالاسم مباشرة على السيرفر
            result = await self.execute_mikrotik_reset_command_by_name(search_query.strip(), chat_id, self.bot_token)

            if not result["success"]:
                message = f"""🔄 **تصفير مباشر بالاسم**

❌ **فشلت العملية!**

{result['error']}"""

                keyboard = [
                    [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_by_name")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_server")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # رسالة النجاح
            escaped_saq = self._escape_markdown(result['saq'])
            escaped_chat_id = self._escape_markdown(result['chat_id'])

            message = f"""🔄 **تصفير مباشر بالاسم**

✅ **تم تنفيذ أمر التصفير بالاسم مباشرة على السيرفر بنجاح!**

📊 **المعلومات المستخدمة:**
• **saq (اسم المستخدم):** `{escaped_saq}`
• **ssaq (معرف المحادثة):** `{escaped_chat_id}`
• **sssaq (رمز البوت):** محجوب للأمان

🔄 **العمليات المطبقة على السيرفر:**
• **البحث:** عن المستخدمين المعطلين بالاسم `{escaped_saq}`
• **التصفير:** `ip hot user reset` لكل مستخدم معطل
• **التفعيل:** `ip hot user en` لكل مستخدم بعد التصفير
• **الإشعار:** إرسال تلقائي من السيرفر لكل مستخدم مع اسمه وتعليقه

⚡ **تم التنفيذ مباشرة على MikroTik Server**

⏰ **وقت العملية:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            keyboard = [
                [InlineKeyboardButton("🔄 تصفير جديد", callback_data="direct_reset_by_name")],
                [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_server")],
                [InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            logger.info(f"تم تنفيذ التصفير بالاسم مباشرة على السيرفر بنجاح: saq='{result['saq']}', chat_id='{result['chat_id']}'")

        except Exception as e:
            logger.error(f"Error in process_direct_reset_by_name: {e}")
            message = f"""🔄 **تصفير مباشر بالاسم**

❌ **حدث خطأ غير متوقع**

{str(e)}

يرجى المحاولة مرة أخرى أو التواصل مع المدير."""

            keyboard = [
                [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_by_name")],
                [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_server")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

    async def process_direct_reset_special_by_comment(self, update, search_query):
        """معالج التصفير الخاص بالتعليق"""
        try:
            # التحقق من صحة الإدخال
            if not search_query or not search_query.strip():
                message = """⭐ تصفير خاص بالتعليق

❌ خطأ في الإدخال

يرجى إدخال جزء من التعليق المراد البحث عنه وتصفيره.

مثال صحيح: 123 (رقم أو جزء من التعليق)"""

                keyboard = [
                    [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_special_by_comment")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_special")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    reply_markup=reply_markup
                )
                return

            # عرض رسالة المعالجة
            await update.message.reply_text("⭐ **جاري تنفيذ التصفير الخاص بالتعليق مباشرة على السيرفر...**")

            # الحصول على معلومات البوت والمحادثة
            chat_id = str(update.effective_chat.id)

            # تنفيذ أمر التصفير الخاص بالتعليق مباشرة على السيرفر
            result = await self.execute_mikrotik_special_reset_command_by_comment(search_query.strip(), chat_id, self.bot_token)

            if not result["success"]:
                message = f"""⭐ تصفير خاص بالتعليق

❌ فشلت العملية!

{result['error']}"""

                keyboard = [
                    [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_special_by_comment")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_special")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    reply_markup=reply_markup
                )
                return

            # رسالة النجاح

            message = f"""⭐ تصفير خاص بالتعليق

✅ تم تنفيذ التصفير الخاص بالتعليق مباشرة على السيرفر بنجاح!

📊 المعلومات المستخدمة:
• جزء التعليق المبحوث: {result['saq']}
• معرف المحادثة: {result['chat_id']}
• رمز البوت: محجوب للأمان

⭐ العمليات الخاصة المطبقة على السيرفر:
• البحث: عن المستخدمين المعطلين الذين يحتوي تعليقهم على {result['saq']}
• معالجة التواريخ: استخراج التاريخ والوقت من النظام
• معالجة التعليقات: استخراج البيانات من التعليق
• تحديث البريد: تعيين البريد إلى <EMAIL>
• التصفير: ip hot user reset لكل مستخدم معطل
• التفعيل: ip hot user en لكل مستخدم بعد التصفير
• حذف الـ Host: ip hot h remove من قائمة الـ Hotspot Host
• الإشعار: إرسال تلقائي مفصل من السيرفر مع التاريخ والوقت

⚡ تم التنفيذ مباشرة على MikroTik Server

⏰ وقت العملية: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            keyboard = [
                [InlineKeyboardButton("⭐ تصفير خاص جديد", callback_data="direct_reset_special_by_comment")],
                [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_special")],
                [InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

            logger.info(f"تم تنفيذ التصفير الخاص بالتعليق مباشرة على السيرفر بنجاح: saq='{result['saq']}', chat_id='{result['chat_id']}'")

        except Exception as e:
            logger.error(f"Error in process_direct_reset_special_by_comment: {e}")
            message = f"""⭐ تصفير خاص بالتعليق

❌ حدث خطأ غير متوقع

{str(e)}

يرجى المحاولة مرة أخرى أو التواصل مع المدير."""

            keyboard = [
                [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_special_by_comment")],
                [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_special")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                reply_markup=reply_markup
            )

    async def process_direct_reset_special_by_name(self, update, search_query):
        """معالج التصفير الخاص بالاسم"""
        try:
            # التحقق من صحة الإدخال
            if not search_query or not search_query.strip():
                message = """⭐ **تصفير خاص بالاسم**

❌ **خطأ في الإدخال**

يرجى إدخال اسم المستخدم المراد تصفيره.

**مثال صحيح:** `user123`"""

                keyboard = [
                    [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_special_by_name")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_special")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # عرض رسالة المعالجة
            await update.message.reply_text("⭐ **جاري تنفيذ التصفير الخاص بالاسم مباشرة على السيرفر...**")

            # الحصول على معلومات البوت والمحادثة
            chat_id = str(update.effective_chat.id)

            # تنفيذ أمر التصفير الخاص بالاسم مباشرة على السيرفر
            result = await self.execute_mikrotik_special_reset_command_by_name(search_query.strip(), chat_id, self.bot_token)

            if not result["success"]:
                message = f"""⭐ **تصفير خاص بالاسم**

❌ **فشلت العملية!**

{result['error']}"""

                keyboard = [
                    [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_special_by_name")],
                    [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_special")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await update.message.reply_text(
                    message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                return

            # رسالة النجاح
            escaped_saq = self._escape_markdown(result['saq'])
            escaped_chat_id = self._escape_markdown(result['chat_id'])

            message = f"""⭐ **تصفير خاص بالاسم**

✅ **تم تنفيذ التصفير الخاص بالاسم مباشرة على السيرفر بنجاح!**

📊 **المعلومات المستخدمة:**
• **sawq (اسم المستخدم):** `{escaped_saq}`
• **ssawq (معرف المحادثة):** `{escaped_chat_id}`
• **sssawq (رمز البوت):** محجوب للأمان

⭐ **العمليات الخاصة المطبقة على السيرفر:**
• **البحث:** عن المستخدمين المعطلين بالاسم `{escaped_saq}`
• **معالجة التواريخ:** استخراج التاريخ والوقت من النظام
• **معالجة التعليقات:** استخراج البيانات من التعليق
• **تحديث البريد:** تعيين البريد إلى `<EMAIL>`
• **التصفير:** `ip hot user reset` لكل مستخدم معطل
• **التفعيل:** `ip hot user en` لكل مستخدم بعد التصفير
• **حذف الـ Host:** `ip hot h remove` من قائمة الـ Hotspot Host
• **الإشعار:** إرسال تلقائي مفصل من السيرفر مع التاريخ والوقت

⚡ **تم التنفيذ مباشرة على MikroTik Server**

⏰ **وقت العملية:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

            keyboard = [
                [InlineKeyboardButton("⭐ تصفير خاص جديد", callback_data="direct_reset_special_by_name")],
                [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_special")],
                [InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            logger.info(f"تم تنفيذ التصفير الخاص بالاسم مباشرة على السيرفر بنجاح: saq='{result['saq']}', chat_id='{result['chat_id']}'")

        except Exception as e:
            logger.error(f"Error in process_direct_reset_special_by_name: {e}")
            message = f"""⭐ **تصفير خاص بالاسم**

❌ **حدث خطأ غير متوقع**

`{str(e)}`

يرجى المحاولة مرة أخرى أو التواصل مع المدير."""

            keyboard = [
                [InlineKeyboardButton("🔄 محاولة جديدة", callback_data="direct_reset_special_by_name")],
                [InlineKeyboardButton("🔙 العودة", callback_data="direct_reset_special")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

    async def handle_exact_reset_data(self, query, callback_data):
        """Handle reset data request with confirmation"""
        try:
            # Extract user index
            user_index = int(callback_data.replace("exact_reset_data_", ""))

            if not hasattr(self, 'exact_search_results') or not self.exact_search_results:
                await query.answer("❌ لا توجد نتائج بحث متاحة", show_alert=True)
                return

            users = self.exact_search_results['users']
            if user_index >= len(users):
                await query.answer("❌ مستخدم غير صالح", show_alert=True)
                return

            user = users[user_index]
            username = self._escape_markdown(user['username'])

            # Format current data usage
            upload_text = self._format_bytes(user['bytes_in'])
            download_text = self._format_bytes(user['bytes_out'])
            total_used = user['bytes_in'] + user['bytes_out']
            total_used_text = self._format_bytes(total_used)

            message = f"""🔄 **تصفير البيانات**

👤 **المستخدم:** `{username}`

📊 **البيانات الحالية:**
• 📥 **تحميل:** {download_text}
• 📤 **رفع:** {upload_text}
• 📈 **إجمالي:** {total_used_text}

⚠️ **تأكيد العملية:**
سيتم إعادة تعيين عدادات التحميل والرفع إلى الصفر.
جميع البيانات الأخرى ستبقى كما هي.

**هل تريد المتابعة؟**"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ تأكيد التصفير", callback_data=f"confirm_exact_reset_{user_index}"),
                    InlineKeyboardButton("❌ إلغاء", callback_data="back_to_exact_results")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in handle_exact_reset_data: {e}")
            await query.answer("❌ حدث خطأ", show_alert=True)

    async def handle_exact_restore_card(self, query, callback_data):
        """Handle restore to card request with confirmation"""
        try:
            # Extract user index
            user_index = int(callback_data.replace("exact_restore_card_", ""))

            if not hasattr(self, 'exact_search_results') or not self.exact_search_results:
                await query.answer("❌ لا توجد نتائج بحث متاحة", show_alert=True)
                return

            users = self.exact_search_results['users']
            search_term = self.exact_search_results['search_term']

            if user_index >= len(users):
                await query.answer("❌ مستخدم غير صالح", show_alert=True)
                return

            user = users[user_index]
            username = self._escape_markdown(user['username'])
            comment = self._escape_markdown(user['comment']) if user['comment'] else 'غير متوفر'
            profile = self._escape_markdown(user['profile'])
            escaped_search_term = self._escape_markdown(search_term)

            message = f"""📋 **إرجاع لكارت**

👤 **المستخدم الحالي:** `{username}`
💬 **التعليق الحالي:** {comment}
📋 **الملف الشخصي:** {profile}

🔄 **التغييرات المطلوبة:**
• **الاسم الجديد:** `{escaped_search_term}`
• **كلمة المرور:** سيتم حذفها (فارغة)
• **التعليق:** سيتم حذفه (فارغ)
• **الملف الشخصي:** سيبقى كما هو (`{profile}`)

⚠️ **تأكيد العملية:**
سيتم تغيير اسم المستخدم وحذف كلمة المرور والتعليق.

**هل تريد المتابعة؟**"""

            keyboard = [
                [
                    InlineKeyboardButton("✅ تأكيد الإرجاع", callback_data=f"confirm_exact_restore_{user_index}"),
                    InlineKeyboardButton("❌ إلغاء", callback_data="back_to_exact_results")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in handle_exact_restore_card: {e}")
            await query.answer("❌ حدث خطأ", show_alert=True)

    async def confirm_exact_reset_data(self, query, callback_data):
        """Confirm and execute data reset"""
        try:
            # Extract user index
            user_index = int(callback_data.replace("confirm_exact_reset_", ""))

            if not hasattr(self, 'exact_search_results') or not self.exact_search_results:
                await query.answer("❌ لا توجد نتائج بحث متاحة", show_alert=True)
                return

            users = self.exact_search_results['users']
            if user_index >= len(users):
                await query.answer("❌ مستخدم غير صالح", show_alert=True)
                return

            user = users[user_index]
            user_id = user['id']
            username = user['username']

            await query.edit_message_text("🔄 **جاري تصفير البيانات...**")

            # Reset data usage
            result = await self.reset_user_data_usage(user_id)

            if result["success"]:
                escaped_username = self._escape_markdown(username)
                message = f"""🔄 **تصفير البيانات**

✅ **تم بنجاح!**

👤 **المستخدم:** `{escaped_username}`

📊 **النتيجة:**
• تم إعادة تعيين عدادات التحميل والرفع إلى الصفر
• جميع البيانات الأخرى محفوظة
• المستخدم جاهز للاستخدام مرة أخرى

⏰ **وقت العملية:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

                logger.info(f"Successfully reset data usage for user: {username}")

            else:
                escaped_username = self._escape_markdown(username)
                message = f"""🔄 **تصفير البيانات**

❌ **فشلت العملية!**

👤 **المستخدم:** `{escaped_username}`

**السبب:** {result['error']}

يرجى المحاولة مرة أخرى أو التواصل مع المدير."""

                logger.error(f"Failed to reset data usage for user {username}: {result['error']}")

            keyboard = [
                [InlineKeyboardButton("🔙 العودة للنتائج", callback_data="back_to_exact_results")],
                [InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in confirm_exact_reset_data: {e}")
            await query.edit_message_text(
                f"❌ **حدث خطأ أثناء تصفير البيانات**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def confirm_exact_restore_card(self, query, callback_data):
        """Confirm and execute restore to card"""
        try:
            # Extract user index
            user_index = int(callback_data.replace("confirm_exact_restore_", ""))

            if not hasattr(self, 'exact_search_results') or not self.exact_search_results:
                await query.answer("❌ لا توجد نتائج بحث متاحة", show_alert=True)
                return

            users = self.exact_search_results['users']
            search_term = self.exact_search_results['search_term']

            if user_index >= len(users):
                await query.answer("❌ مستخدم غير صالح", show_alert=True)
                return

            user = users[user_index]
            user_id = user['id']
            old_username = user['username']

            await query.edit_message_text("📋 **جاري إرجاع لكارت...**")

            # Restore to card
            result = await self.restore_user_to_card(user_id, search_term)

            if result["success"]:
                escaped_old_username = self._escape_markdown(old_username)
                escaped_new_username = self._escape_markdown(search_term)
                message = f"""📋 **إرجاع لكارت**

✅ **تم بنجاح!**

🔄 **التغييرات المطبقة:**
• **الاسم القديم:** `{escaped_old_username}`
• **الاسم الجديد:** `{escaped_new_username}`
• **كلمة المرور:** تم حذفها
• **التعليق:** تم حذفه
• **الملف الشخصي:** محفوظ كما هو

✅ **النتيجة:**
المستخدم أصبح جاهز كبطاقة جديدة برقم `{escaped_new_username}`

⏰ **وقت العملية:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

                logger.info(f"Successfully restored user {old_username} to card {search_term}")

            else:
                escaped_old_username = self._escape_markdown(old_username)
                message = f"""📋 **إرجاع لكارت**

❌ **فشلت العملية!**

👤 **المستخدم:** `{escaped_old_username}`

**السبب:** {result['error']}

يرجى المحاولة مرة أخرى أو التواصل مع المدير."""

                logger.error(f"Failed to restore user {old_username} to card: {result['error']}")

            keyboard = [
                [InlineKeyboardButton("🔙 العودة للنتائج", callback_data="back_to_exact_results")],
                [InlineKeyboardButton("🏠 القائمة الرئيسية", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

        except Exception as e:
            logger.error(f"Error in confirm_exact_restore_card: {e}")
            await query.edit_message_text(
                f"❌ **حدث خطأ أثناء إرجاع لكارت**\n\n{str(e)}",
                parse_mode=ParseMode.MARKDOWN
            )

    async def reset_user_data_usage(self, user_id):
        """Reset user data usage counters to zero"""
        try:
            connection = self.create_connection()
            api = connection.get_api()

            hotspot_users = api.get_resource('/ip/hotspot/user')

            # Reset bytes-in and bytes-out to 0
            hotspot_users.set(id=user_id, **{
                'bytes-in': '0',
                'bytes-out': '0'
            })

            connection.disconnect()

            logger.info(f"Successfully reset data usage for user ID: {user_id}")
            return {"success": True}

        except Exception as e:
            logger.error(f"Error resetting data usage for user {user_id}: {e}")
            return {"success": False, "error": str(e)}

    async def restore_user_to_card(self, user_id, new_username):
        """Restore user to card by changing username and clearing password/comment"""
        try:
            connection = self.create_connection()
            api = connection.get_api()

            hotspot_users = api.get_resource('/ip/hotspot/user')

            # Update user: change name, clear password and comment
            hotspot_users.set(id=user_id, **{
                'name': new_username,
                'password': '',
                'comment': ''
            })

            connection.disconnect()

            logger.info(f"Successfully restored user ID {user_id} to card with username: {new_username}")
            return {"success": True}

        except Exception as e:
            logger.error(f"Error restoring user {user_id} to card: {e}")
            return {"success": False, "error": str(e)}

    def run(self):
        """Start the bot"""
        logger.info(f"Starting {self.system_name}...")
        logger.info(f"Authorized users: {len(self.authorized_users)}")
        logger.info(f"MikroTik device: {self.actual_host}:{self.mikrotik_port}")

        print(f"🤖 {self.system_name} is running!")
        print(f"📱 Send /start in Telegram to begin")
        print(f"⚠️  Press Ctrl+C to stop")

        # Start the bot with proper async handling
        async def run_with_startup():
            """Run the bot with startup notification and continuous operation"""
            try:
                logger.info("Initializing Telegram application...")

                # Initialize the application
                await self.application.initialize()
                logger.info("Application initialized successfully")

                # Start the application
                await self.application.start()
                logger.info("Application started successfully")

                # Start polling FIRST to ensure bot is ready to receive messages
                logger.info("Starting polling...")
                await self.application.updater.start_polling(drop_pending_updates=True)
                logger.info("Polling started successfully - Bot is now listening for messages")

                # Wait a moment for polling to stabilize
                await asyncio.sleep(2)

                # Send startup notification AFTER polling is active
                logger.info("Sending startup notifications to authorized users...")
                try:
                    await self.send_startup_notification()
                    logger.info("Startup notifications sent successfully")
                except Exception as e:
                    logger.error(f"Error sending startup notifications: {e}")
                    # Don't stop the bot if notification fails

                print("✅ Bot is fully operational and ready to receive commands!")

                # Start health monitoring in background
                health_task = asyncio.create_task(self.monitor_bot_health())
                logger.info("Bot health monitoring started")

                # Keep the bot running indefinitely
                logger.info("Bot entering idle state - waiting for messages...")
                try:
                    # Simple method to keep the bot running
                    logger.info("Bot is now running and waiting for messages...")

                    # Keep running until interrupted
                    while True:
                        await asyncio.sleep(1)  # Sleep for 1 second and check again

                except KeyboardInterrupt:
                    logger.info("Bot interrupted by user")
                except Exception as e:
                    logger.error(f"Error in idle state: {e}")
                    raise
                finally:
                    # Cancel health monitoring
                    health_task.cancel()
                    try:
                        await health_task
                    except asyncio.CancelledError:
                        pass

            except Exception as e:
                logger.error(f"Critical error in bot operation: {e}")
                import traceback
                logger.error(f"Traceback: {traceback.format_exc()}")
                raise
            finally:
                # Cleanup when shutting down
                logger.info("Shutting down bot...")
                try:
                    # Stop the updater first
                    if self.application.updater.running:
                        await self.application.updater.stop()
                        logger.info("Updater stopped")

                    # Then stop the application
                    await self.application.stop()
                    await self.application.shutdown()
                    logger.info("Bot shutdown completed")
                except Exception as e:
                    logger.error(f"Error during shutdown: {e}")

        # Run the bot with proper error handling
        import asyncio
        try:
            asyncio.run(run_with_startup())
        except KeyboardInterrupt:
            logger.info("Bot stopped by user (Ctrl+C)")
            print("\n🛑 Bot stopped by user")
        except Exception as e:
            logger.error(f"Fatal error in bot execution: {e}")
            print(f"\n❌ Fatal error: {e}")
            raise

def main():
    """Main function"""
    try:
        logger.info("Creating bot instance...")
        bot = SimpleMikroTikBot()
        logger.info("Bot instance created successfully")

        logger.info("Starting bot...")
        bot.run()

    except KeyboardInterrupt:
        logger.info("Bot interrupted by user")
        print("\n🛑 Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error in main: {e}")
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        print(f"\n❌ Fatal Error: {e}")
        print("Check the log file for detailed error information.")

if __name__ == "__main__":
    main()