#!/usr/bin/env python3
"""
Test script to verify startup notification functionality
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from simple_bot2 import SimpleMikroTikBot

async def test_startup_notification():
    """Test the startup notification functionality"""
    print("🧪 Testing startup notification...")
    
    try:
        # Create bot instance
        bot = SimpleMikroTikBot()
        print("✅ Bot instance created successfully")
        
        # Test MikroTik connection
        print("🔧 Testing MikroTik connection...")
        success, message = bot.test_connection_simple()
        
        if success:
            print(f"✅ MikroTik connection successful: {message}")
        else:
            print(f"❌ MikroTik connection failed: {message}")
        
        # Test startup notification (without actually sending)
        print("📱 Testing startup notification logic...")
        
        # Simulate the startup notification logic
        connection_status = "❌ غير متصل"
        try:
            success, message = bot.test_connection_simple()
            if success:
                connection_status = "✅ متصل"
                print(f"✅ Connection status would be: {connection_status}")
            else:
                connection_status = f"❌ خطأ: {message}"
                print(f"❌ Connection status would be: {connection_status}")
        except Exception as e:
            connection_status = f"❌ خطأ: {str(e)}"
            print(f"❌ Connection status would be: {connection_status}")
        
        # Show what the startup message would look like
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        startup_message = f"""🤖 **مرحبا! البوت جاهز للعمل**

✅ **حالة النظام:**
• الاتصال بـ MikroTik: {connection_status}
• الجهاز: {bot.actual_host}:{bot.mikrotik_port}
• المستخدمون المصرحون: {len(bot.authorized_users)}
• وقت البدء: {current_time}

🎯 **البوت جاهز لاستقبال الأوامر!**

اختر من القائمة أدناه:"""

        print("\n" + "="*50)
        print("📋 STARTUP MESSAGE PREVIEW:")
        print("="*50)
        print(startup_message)
        print("="*50)
        
        print("\n✅ Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_startup_notification())
