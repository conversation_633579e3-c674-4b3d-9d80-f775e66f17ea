@echo off
setlocal EnableDelayedExpansion

echo Building Simple MikroTik Bot executable...

:: Set UTF-8 encoding for console
chcp 65001 >nul

:: Check if Python is installed
where python >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: Python is not installed. Please install Python 3.7 or higher.
    pause
    exit /b 1
)

:: Check if PyInstaller is installed
where pyinstaller >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Installing PyInstaller...
    pip install pyinstaller
)

:: Check if virtual environment exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo Virtual environment not found. Creating one...
    python -m venv venv
    call venv\Scripts\activate.bat
)

:: Install required packages
echo Installing required packages...
pip install python-telegram-bot==20.0 routeros-api

:: Build the executable
echo Building executable...
pyinstaller --onefile --noconsole --name SimpleMikroTikBot simple_bot2-1362ca1f-ee8b-4bf0-8042-588953e1207a.py

:: Check if the executable was created
if exist "dist\SimpleMikroTikBot.exe" (
    echo Success: Executable created in dist\SimpleMikroTikBot.exe
) else (
    echo Error: Failed to create executable. Check PyInstaller output for details.
    pause
    exit /b 1
)

echo Done!
pause