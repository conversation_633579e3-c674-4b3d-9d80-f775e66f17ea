```python
import telebot
from routeros_api import RouterOsApiPool
import re

# إعدادات بوت التيليجرام
BOT_TOKEN = 'YOUR_BOT_TOKEN_HERE'  # حط توكن البوت بتاعك هنا

# إعدادات الميكروتيك
MIKROTIK_IP = 'YOUR_MIKROTIK_IP'  # عنوان IP للميكروتيك
MIKROTIK_USERNAME = 'YOUR_MIKROTIK_USERNAME'  # اسم المستخدم
MIKROTIK_PASSWORD = 'YOUR_MIKROTIK_PASSWORD'  # كلمة السر

# تهيئة بوت التيليجرام
bot = telebot.TeleBot(BOT_TOKEN)

# الاتصال بالميكروتيك
def connect_to_mikrotik():
    try:
        connection = RouterOsApiPool(
            host=MIKROTIK_IP,
            username=MIKROTIK_USERNAME,
            password=MIKROTIK_PASSWORD,
            plaintext_login=True
        )
        api = connection.get_api()
        return api
    except Exception as e:
        return f"خطأ في الاتصال بالميكروتيك: {str(e)}"

# دالة للبحث عن يوزر بناءً على التعليق
def find_and_update_user(comment):
    api = connect_to_mikrotik()
    if isinstance(api, str):  # لو فيه خطأ في الاتصال
        return api

    try:
        # جلب قائمة اليوزرات من الميكروتيك
        users = api.get_resource('/ppp/secret').get()
        for user in users:
            user_comment = user.get('comment', '')
            # البحث عن تطابق التعليق
            if user_comment == comment:
                user_id = user['.id']
                username = user['name']
                # استبدال اسم اليوزر بالتعليق
                api.get_resource('/ppp/secret').set(
                    id=user_id,
                    name=user_comment,  # استبدال اسم اليوزر بالتعليق
                    comment='',         # حذف التعليق
                    password=''         # حذف الباسورد
                )
                return f"تم تحديث اليوزر '{username}' بنجاح! الاسم الجديد: {user_comment}"
        return "لم يتم العثور على يوزر بالتعليق المحدد."
    except Exception as e:
        return f"خطأ أثناء تحديث اليوزر: {str(e)}"
    finally:
        api.get_connection().disconnect()

# معالجة الأوامر في التيليجرام
@bot.message_handler(commands=['changeuser'])
def change_user(message):
    try:
        # استخراج التعليق من الأمر
        comment = message.text.split(maxsplit=1)[1] if len(message.text.split()) > 1 else None
        if not comment:
            bot.reply_to(message, "يرجى إدخال تعليق للبحث. مثال: /changeuser comment_text")
            return
        result = find_and_update_user(comment)
        bot.reply_to(message, result)
    except Exception as e:
        bot.reply_to(message, f"خطأ: {str(e)}")

# بدء البوت
bot.polling()
```

### شرح الإسكربت:
1. **الإعدادات**:
   - `BOT_TOKEN`: حط توكن البوت بتاعك من BotFather في التيليجرام.
   - `MIKROTIK_IP`, `MIKROTIK_USERNAME`, `MIKROTIK_PASSWORD`: إعدادات الميكروتيك بتاعتك.
   
2. **الاتصال بالميكروتيك**:
   - الإسكربت بيستخدم مكتبة `routeros_api` للاتصال بالميكروتيك عبر API.
   - لو بتستخدم SSH، ممكن نستبدل الجزء ده بمكتبة زي `paramiko`.

3. **البحث وتحديث اليوزر**:
   - الإسكربت بيدور على يوزر في `/ppp/secret` عنده تعليق مطابق للي المستخدم بعته.
   - لو لقى اليوزر، بيستبدل اسم اليوزر بالتعليق، وبيحذف التعليق والباسورد.

4. **أمر التيليجرام**:
   - المستخدم بيبعت أمر زي: `/changeuser comment_text`
   - البوت بيدور على اليوزر اللي تعليقه مطابق لـ `comment_text` وينفذ التغييرات.

### كيف تستخدم الإسكربت:
1. ثبت المكتبات المطلوبة:
   ```bash
   pip install pyTelegramBotAPI routeros_api
   ```
2. استبدل القيم في الإسكربت (`BOT_TOKEN`, `MIKROTIK_IP`, إلخ) بالبيانات بتاعتك.
3. شغل الإسكربت:
   ```bash
   python mikrotik_user_manager.py
   ```
4. في التيليجرام، ابعت أمر زي:
   ```
   /changeuser test_comment
   ```
   لو فيه يوزر عنده تعليق `test_comment`， هيتغير اسمه لـ `test_comment` ويتحذف التعليق والباسورد.

### ملاحظات:
- لو عايز تغير طريقة البحث (مثلاً، تطابق جزئي بدل تطابق كامل)، أو تضيف شروط إضافية، قولي وأعدل الإسكربت.
- لو بتستخدم SSH بدل API، قولي وأغير الكود ليستخدم `paramiko`.
- لو عايز إضافات زي تسجيل اللوج أو حماية إضافية، ممكن نضيفها.

قولي لو محتاج أي تعديل أو توضيح إضافي! 🚀