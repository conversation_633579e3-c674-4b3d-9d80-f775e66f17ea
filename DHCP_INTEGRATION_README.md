# تكامل DHCP مع البحث في HotSpot
## DHCP Integration with HotSpot Search

### الوصف | Description

تم تطوير ميزة جديدة تربط بين بيانات HotSpot و DHCP leases لعرض أسماء الأجهزة النشطة عند البحث في HotSpot.

A new feature has been developed that links HotSpot data with DHCP leases to display active device hostnames when searching in HotSpot.

### الميزات الجديدة | New Features

#### 1. عرض أسماء الأجهزة النشطة | Display Active Device Hostnames
- عند البحث عن مستخدم في HotSpot (بالاسم أو التعليق)
- إذا كان المستخدم متصل حالياً، يتم عرض اسم الجهاز من DHCP lease
- يتم ربط البيانات عبر MAC address

#### 2. معلومات إضافية | Additional Information
- **🏠 اسم الجهاز**: من DHCP hostname
- **🔗 MAC Address**: عنوان MAC للجهاز المتصل
- **🌐 IP Address**: عنوان IP النشط
- **📊 حالة DHCP**: حالة الـ lease

### كيفية العمل | How It Works

```
1. البحث في HotSpot → Search in HotSpot
2. العثور على مستخدم → Find user
3. التحقق من الاتصال النشط → Check active connection
4. الحصول على MAC address → Get MAC address
5. البحث في DHCP leases → Search DHCP leases
6. عرض hostname النشط → Display active hostname
```

### الوظائف المضافة | Added Functions

#### `get_dhcp_leases()`
- تحصل على جميع DHCP leases من MikroTik
- تنظم البيانات حسب MAC address للبحث السريع
- ترجع dictionary مع معلومات كل lease

#### `get_hotspot_active_users_with_mac()`
- تحصل على المستخدمين النشطين في HotSpot مع MAC addresses
- تربط اسم المستخدم بـ MAC address و IP
- تشمل معلومات إضافية مثل uptime و bytes transferred

#### `get_hostname_from_dhcp_lease(mac_address)`
- تبحث عن hostname في DHCP leases باستخدام MAC address
- ترجع معلومات الجهاز إذا وُجد
- تتعامل مع تنسيقات MAC address المختلفة

### التحسينات على الوظائف الموجودة | Improvements to Existing Functions

#### `search_hotspot_user_by_name()`
- تم تحسينها لتشمل MAC addresses
- تعرض hostname من DHCP عند توفره
- تظهر معلومات إضافية للمستخدمين المتصلين

#### `search_hotspot_user_by_comment()`
- نفس التحسينات المطبقة على البحث بالاسم
- تدعم عرض hostname و MAC address
- تحافظ على جميع الميزات الأصلية

### مثال على النتيجة | Example Output

```
🔍 البحث: ahmed

📊 النتائج: 2 مستخدم
📄 الصفحة: 1 من 1

1. ahmed123 ✅ 🟢
   📋 الملف: default
   🏠 اسم الجهاز: Ahmed-Laptop
   🔗 MAC: AA:BB:CC:DD:EE:FF
   📧 البريد: <EMAIL>

2. ahmed_mobile ✅ ⚪
   📋 الملف: mobile
```

### متطلبات النظام | System Requirements

- MikroTik RouterOS مع تفعيل API
- DHCP Server مفعل ويعمل
- HotSpot مُعد ويعمل
- صلاحيات قراءة لـ:
  - `/ip/hotspot/user`
  - `/ip/hotspot/active`
  - `/ip/dhcp-server/lease`

### الاختبار | Testing

لاختبار الميزة الجديدة:

```bash
python test_dhcp_integration.py
```

أو استخدم البوت مباشرة:
1. أرسل `/start`
2. اختر "🏨 البحث في الهوت سبوت"
3. اختر "👤 بالاسم" أو "💬 بالتعليق"
4. أدخل نص البحث

### ملاحظات مهمة | Important Notes

- الميزة تعمل فقط مع المستخدمين المتصلين حالياً
- تتطلب أن يكون DHCP Server يسجل hostnames
- MAC address يجب أن يكون متطابق بين HotSpot و DHCP
- قد تحتاج لصلاحيات إضافية في MikroTik

### استكشاف الأخطاء | Troubleshooting

#### لا تظهر أسماء الأجهزة
- تأكد من أن DHCP Server يسجل hostnames
- تحقق من أن الأجهزة ترسل hostname في DHCP request
- تأكد من صلاحيات قراءة DHCP leases

#### MAC addresses غير متطابقة
- تحقق من تنسيق MAC address في كلا النظامين
- الكود يتعامل مع تنسيقات مختلفة تلقائياً

#### بطء في الاستجابة
- الميزة تتطلب استعلامات إضافية من MikroTik
- يمكن تحسين الأداء بحفظ النتائج مؤقتاً

### التطوير المستقبلي | Future Development

- إضافة cache للنتائج لتحسين الأداء
- دعم البحث المباشر بـ MAC address
- إضافة معلومات إضافية من DHCP
- تحسين واجهة المستخدم
