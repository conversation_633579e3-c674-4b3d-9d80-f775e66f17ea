#!/usr/bin/env python3
"""
Test script for Star Number Search functionality
"""

import asyncio
import sys
import os
import re

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_star_search_regex():
    """Test the star search regex functionality"""
    print("⭐ Testing Star Number Search Regex...")
    
    # Test cases for exact matching
    test_cases = [
        {
            "target": "12345",
            "comments": [
                "مستخدم تجريبي *12345",      # Should match
                "عميل جديد *12345 نهاية",    # Should match
                "رقم *123456",               # Should NOT match
                "رقم *1234",                 # Should NOT match
                "نص *12345678",             # Should NOT match
                "نص *123",                   # Should NOT match
                "مستخدم *12345 وسط",        # Should match
                "بداية *12345",             # Should match
                "لا يوجد نجمة 12345",        # Should NOT match
                "*12345",                    # Should match
                "test*12345",                # Should match
                "test*123456",               # Should NOT match
            ]
        },
        {
            "target": "999",
            "comments": [
                "اختبار *999",              # Should match
                "اختبار *9999",             # Should NOT match
                "اختبار *99",               # Should NOT match
                "نص *999 نهاية",            # Should match
                "بداية *999",               # Should match
                "*999",                      # Should match
                "test*999test",              # Should match
                "test*9999",                 # Should NOT match
            ]
        },
        {
            "target": "0",
            "comments": [
                "رقم *0",                    # Should match
                "رقم *01",                   # Should NOT match
                "رقم *10",                   # Should NOT match
                "*0",                        # Should match
                "test*0 end",                # Should match
            ]
        }
    ]
    
    total_tests = 0
    passed_tests = 0
    
    for test_case in test_cases:
        target = test_case["target"]
        comments = test_case["comments"]
        
        print(f"\n🔍 Testing target number: {target}")
        
        for comment in comments:
            total_tests += 1
            
            # Test the exact matching logic
            pattern = rf'\*{re.escape(target)}\b'
            found = bool(re.search(pattern, comment))
            
            # Determine expected result
            # Should match if comment contains *target followed by word boundary
            expected = False
            if f"*{target}" in comment:
                # Check if it's followed by a word boundary (not a digit)
                star_pos = comment.find(f"*{target}")
                if star_pos != -1:
                    end_pos = star_pos + len(f"*{target}")
                    if end_pos >= len(comment) or not comment[end_pos].isdigit():
                        expected = True
            
            status = "✅" if found == expected else "❌"
            result_text = "MATCH" if found else "NO MATCH"
            expected_text = "MATCH" if expected else "NO MATCH"
            
            print(f"   {status} '{comment}' → {result_text} (Expected: {expected_text})")
            
            if found == expected:
                passed_tests += 1
            else:
                print(f"      ⚠️ Test failed! Pattern: {pattern}")
    
    print(f"\n📊 Test Results: {passed_tests}/{total_tests} passed")
    
    if passed_tests == total_tests:
        print("✅ All regex tests passed!")
    else:
        print(f"❌ {total_tests - passed_tests} tests failed!")
    
    return passed_tests == total_tests

async def test_input_validation():
    """Test input validation"""
    print("\n🔧 Testing input validation...")
    
    test_inputs = [
        ("12345", True, "Valid number"),
        ("999", True, "Valid single digit"),
        ("0", True, "Valid zero"),
        ("abc", False, "Letters only"),
        ("123abc", False, "Mixed alphanumeric"),
        ("12.34", False, "Decimal number"),
        ("", False, "Empty string"),
        ("12 34", False, "Number with space"),
        ("12-34", False, "Number with dash"),
        ("12345678901234567890", True, "Very long number"),
    ]
    
    passed = 0
    total = len(test_inputs)
    
    for test_input, expected, description in test_inputs:
        is_valid = test_input.isdigit()
        status = "✅" if is_valid == expected else "❌"
        
        print(f"   {status} '{test_input}' → Valid: {is_valid} ({description})")
        
        if is_valid == expected:
            passed += 1
    
    print(f"\n📊 Validation Results: {passed}/{total} passed")
    
    if passed == total:
        print("✅ All validation tests passed!")
    else:
        print(f"❌ {total - passed} validation tests failed!")
    
    return passed == total

async def main():
    """Run all tests"""
    print("🧪 Testing Star Number Search Functionality")
    print("=" * 50)
    
    try:
        # Test regex functionality
        regex_passed = await test_star_search_regex()
        
        # Test input validation
        validation_passed = await test_input_validation()
        
        print("\n" + "=" * 50)
        print("📋 **Summary of Star Number Search Feature:**")
        print("   ✅ Added new button '⭐ البحث بالنجمة' to main menu")
        print("   ✅ Interactive number input with clear instructions")
        print("   ✅ Exact number matching using word boundaries")
        print("   ✅ Input validation for numeric-only input")
        print("   ✅ Comprehensive error handling and user feedback")
        print("   ✅ Search in HotSpot user comments for *number patterns")
        print("   ✅ Clear results display with user details")
        
        print("\n🎯 **Feature Capabilities:**")
        print("   • Searches for exact number after * symbol")
        print("   • Prevents partial matches (e.g., *12345 won't match *123456)")
        print("   • Validates input to ensure numeric-only")
        print("   • Provides clear feedback for no results")
        print("   • Shows user details including status and profile")
        
        if regex_passed and validation_passed:
            print("\n🚀 All tests passed! Star Number Search feature is ready!")
        else:
            print("\n⚠️ Some tests failed. Please review the implementation.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
