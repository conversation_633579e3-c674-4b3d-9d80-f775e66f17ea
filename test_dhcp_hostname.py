#!/usr/bin/env python3
"""
Test script to verify DHCP hostname integration
"""

import asyncio
import json
import sys
import os

# Add the current directory to the path so we can import simple_bot2
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from simple_bot2 import SimpleMikroTikBot

async def test_dhcp_hostname():
    """Test DHCP hostname functionality"""
    
    # Load configuration
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"Error loading config: {e}")
        return
    
    # Create bot instance
    bot = SimpleMikroTikBot()
    
    print("Testing DHCP hostname integration...")
    
    # Test 1: Get DHCP leases
    print("\n1. Testing DHCP leases retrieval...")
    dhcp_result = await bot.get_dhcp_leases()
    if dhcp_result['success']:
        print(f"✅ Successfully retrieved {len(dhcp_result['leases'])} DHCP leases")
        
        # Show first few leases with hostnames
        count = 0
        for mac, lease in dhcp_result['leases'].items():
            if lease.get('hostname') and lease['hostname'] != 'غير متوفر' and lease['hostname'].strip():
                print(f"   MAC: {mac} -> Hostname: '{lease['hostname']}' (IP: {lease.get('ip', 'N/A')})")
                count += 1
                if count >= 5:  # Show only first 5
                    break
        
        if count == 0:
            print("   ⚠️ No leases with hostnames found")
    else:
        print(f"❌ Failed to get DHCP leases: {dhcp_result['error']}")
        return
    
    # Test 2: Get HotSpot active users with MAC
    print("\n2. Testing HotSpot active users with MAC...")
    hotspot_result = await bot.get_hotspot_active_users_with_mac()
    if hotspot_result['success']:
        print(f"✅ Successfully retrieved {len(hotspot_result['users'])} active HotSpot users")
        
        # Show first few users with MAC addresses
        count = 0
        for username, user_info in hotspot_result['users'].items():
            if user_info.get('mac') and user_info['mac'] != 'غير متوفر':
                print(f"   User: {username} -> MAC: {user_info['mac']} (IP: {user_info.get('ip', 'N/A')})")
                count += 1
                if count >= 3:  # Show only first 3
                    break
        
        if count == 0:
            print("   ⚠️ No active users with MAC addresses found")
    else:
        print(f"❌ Failed to get HotSpot users: {hotspot_result['error']}")
        return
    
    # Test 3: Test hostname lookup for a specific MAC
    print("\n3. Testing hostname lookup...")
    
    # Find a MAC address from active users
    test_mac = None
    for username, user_info in hotspot_result['users'].items():
        if user_info.get('mac') and user_info['mac'] != 'غير متوفر':
            test_mac = user_info['mac']
            print(f"   Testing with MAC: {test_mac} (from user: {username})")
            break
    
    if test_mac:
        hostname_result = await bot.get_hostname_from_dhcp_lease(test_mac)
        if hostname_result:
            print(f"   ✅ Found hostname: '{hostname_result['hostname']}'")
            print(f"      IP: {hostname_result.get('ip', 'N/A')}")
            print(f"      Status: {hostname_result.get('status', 'N/A')}")
        else:
            print(f"   ⚠️ No hostname found for MAC: {test_mac}")
    else:
        print("   ⚠️ No MAC address available for testing")
    
    print("\n4. Testing complete integration...")
    
    # Test the complete flow by searching for a user
    if hotspot_result['users']:
        test_username = list(hotspot_result['users'].keys())[0]
        print(f"   Testing search for user: {test_username}")
        
        # Simulate the search process
        users = await bot.search_hotspot_users_by_name(test_username)
        if users['success'] and users['users']:
            user = users['users'][0]
            print(f"   User found: {user['username']}")
            print(f"   MAC: {user.get('mac_address', 'N/A')}")
            print(f"   DHCP Hostname: {user.get('dhcp_hostname', 'N/A')}")
            
            if user.get('dhcp_hostname') and user['dhcp_hostname'] != 'غير متوفر':
                print("   ✅ DHCP hostname integration working!")
            else:
                print("   ❌ DHCP hostname not found or empty")
        else:
            print(f"   ❌ Failed to search for user: {users.get('error', 'Unknown error')}")
    
    print("\nTest completed!")

if __name__ == "__main__":
    asyncio.run(test_dhcp_hostname())
