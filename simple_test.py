#!/usr/bin/env python3
"""
Simple test to check if the bot can connect and get basic info
"""

import asyncio
import json
import sys
import os

# Add the current directory to the path so we can import simple_bot2
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from simple_bot2 import SimpleMikroTikBot

async def simple_test():
    """Simple test"""
    
    print("Starting simple test...")
    
    # Load configuration
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        print("✅ Config loaded successfully")
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return
    
    # Create bot instance
    try:
        bot = SimpleMikroTikBot()
        print("✅ Bot instance created")
    except Exception as e:
        print(f"❌ Error creating bot: {e}")
        return
    
    # Test connection
    try:
        print("Testing MikroTik connection...")
        # Try to get DHCP leases as a simple test
        dhcp_result = await bot.get_dhcp_leases()
        if dhcp_result['success']:
            print(f"✅ Connection successful! Found {len(dhcp_result['leases'])} DHCP leases")
            
            # Show a few examples
            count = 0
            for mac, lease in dhcp_result['leases'].items():
                hostname = lease.get('hostname', '')
                if hostname and hostname.strip() and hostname != 'غير متوفر':
                    print(f"   Example: MAC {mac} -> Hostname: '{hostname}'")
                    count += 1
                    if count >= 3:
                        break
            
            if count == 0:
                print("   No hostnames found in DHCP leases")
        else:
            print(f"❌ Connection failed: {dhcp_result['error']}")
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(simple_test())
