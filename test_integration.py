#!/usr/bin/env python3
"""
Test the complete DHCP hostname integration
"""

import asyncio
import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from simple_bot2 import SimpleMikroTikBot

async def test_integration():
    """Test the complete integration"""
    
    print("🔍 Testing DHCP hostname integration...")
    
    try:
        # Create bot instance
        bot = SimpleMikroTikBot()
        print("✅ Bot created successfully")
        
        # Test 1: Get all HotSpot users
        print("\n📋 Getting all HotSpot users...")
        all_users = await bot.get_all_hotspot_users()
        
        if all_users['success']:
            print(f"✅ Found {len(all_users['users'])} total HotSpot users")
            
            # Find users with MAC addresses
            users_with_mac = []
            for user in all_users['users']:
                if user.get('mac_address') and user['mac_address'] != 'غير متوفر':
                    users_with_mac.append(user)
            
            print(f"📱 {len(users_with_mac)} users have MAC addresses")
            
            if users_with_mac:
                # Test with first user that has MAC
                test_user = users_with_mac[0]
                print(f"\n🧪 Testing with user: {test_user['username']}")
                print(f"   MAC: {test_user['mac_address']}")
                
                # Test hostname lookup
                hostname_info = await bot.get_hostname_from_dhcp_lease(test_user['mac_address'])
                if hostname_info:
                    print(f"   ✅ Found hostname: '{hostname_info['hostname']}'")
                    print(f"   IP: {hostname_info.get('ip', 'N/A')}")
                else:
                    print(f"   ❌ No hostname found for this MAC")
            else:
                print("   ⚠️ No users with MAC addresses found")
        else:
            print(f"❌ Failed to get HotSpot users: {all_users['error']}")
            return
        
        # Test 2: Search for a specific user
        print("\n🔍 Testing user search...")
        if all_users['users']:
            test_username = all_users['users'][0]['username']
            print(f"   Searching for: {test_username}")
            
            search_result = await bot.search_hotspot_users_by_name(test_username)
            if search_result['success'] and search_result['users']:
                user = search_result['users'][0]
                print(f"   ✅ User found: {user['username']}")
                print(f"   MAC: {user.get('mac_address', 'N/A')}")
                print(f"   DHCP Hostname: {user.get('dhcp_hostname', 'N/A')}")
                
                if user.get('dhcp_hostname') and user['dhcp_hostname'] != 'غير متوفر':
                    print("   🎉 DHCP hostname integration is working!")
                else:
                    print("   ⚠️ No DHCP hostname found for this user")
            else:
                print(f"   ❌ Search failed: {search_result.get('error', 'Unknown error')}")
        
        print("\n✅ Test completed!")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_integration())
